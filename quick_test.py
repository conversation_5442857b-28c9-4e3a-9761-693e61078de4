#!/usr/bin/env python3
"""
快速验证脚本
"""

import asyncio
from rich.console import Console
from scanner_config import get_config, list_available_configs

console = Console()

def test_imports():
    """测试所有必要的导入"""
    console.print("测试导入...")
    
    try:
        import scanner_optimized
        console.print("✓ scanner_optimized 导入成功")
        
        import scanner_config
        console.print("✓ scanner_config 导入成功")
        
        import ccxt.pro
        console.print("✓ ccxt.pro 导入成功")
        
        import pandas_ta
        console.print("✓ pandas_ta 导入成功")
        
        import aiohttp
        console.print("✓ aiohttp 导入成功")
        
        return True
    except ImportError as e:
        console.print(f"✗ 导入失败: {e}")
        return False

def test_config():
    """测试配置系统"""
    console.print("\n测试配置系统...")
    
    try:
        # 测试获取默认配置
        config = get_config('default')
        console.print(f"✓ 默认配置加载成功，处理品种数: {config.top_n}")
        
        # 测试获取所有配置
        configs = list_available_configs()
        console.print(f"✓ 可用配置: {len(configs)} 个")
        
        # 测试保守配置
        conservative_config = get_config('conservative')
        console.print(f"✓ 保守配置加载成功，趋势阈值: {conservative_config.trend_strength_threshold}")
        
        return True
    except Exception as e:
        console.print(f"✗ 配置测试失败: {e}")
        return False

async def test_exchange_connection():
    """测试交易所连接"""
    console.print("\n测试交易所连接...")
    
    try:
        import ccxt.pro
        
        exchange = ccxt.pro.binance({
            'enableRateLimit': True,
            'options': {'defaultType': 'future'},
            'timeout': 10000,  # 10秒超时
        })
        
        # 简单测试：获取服务器时间
        server_time = await asyncio.wait_for(
            asyncio.to_thread(exchange.fetch_time), 
            timeout=10
        )
        
        if server_time:
            console.print("✓ 币安交易所连接成功")
            await exchange.close()
            return True
        else:
            console.print("✗ 无法获取服务器时间")
            await exchange.close()
            return False
            
    except asyncio.TimeoutError:
        console.print("✗ 连接超时")
        return False
    except Exception as e:
        console.print(f"✗ 连接失败: {e}")
        return False

def main():
    """主测试函数"""
    console.print("[bold]快速验证扫描器环境[/bold]\n")
    
    # 测试导入
    import_ok = test_imports()
    
    # 测试配置
    config_ok = test_config()
    
    # 测试交易所连接
    try:
        connection_ok = asyncio.run(test_exchange_connection())
    except Exception as e:
        console.print(f"✗ 交易所连接测试异常: {e}")
        connection_ok = False
    
    # 总结
    console.print("\n" + "="*50)
    console.print("[bold]测试结果总结:[/bold]")
    console.print(f"导入测试: {'✓ 通过' if import_ok else '✗ 失败'}")
    console.print(f"配置测试: {'✓ 通过' if config_ok else '✗ 失败'}")
    console.print(f"连接测试: {'✓ 通过' if connection_ok else '✗ 失败'}")
    
    if import_ok and config_ok and connection_ok:
        console.print("\n[bold green]✓ 所有测试通过！扫描器环境正常[/bold green]")
        console.print("\n可以使用以下命令启动扫描器:")
        console.print("  [cyan]python run_scanner.py[/cyan]")
        console.print("  [cyan]python run_scanner.py --config conservative[/cyan]")
        console.print("  [cyan]python run_scanner.py --list-configs[/cyan]")
    else:
        console.print("\n[bold red]✗ 部分测试失败，请检查环境配置[/bold red]")
        
        if not import_ok:
            console.print("请安装缺失的依赖包")
        if not connection_ok:
            console.print("请检查网络连接")

if __name__ == "__main__":
    main()