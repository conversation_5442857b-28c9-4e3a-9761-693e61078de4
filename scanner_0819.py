# bottom_detector_v1.py

import sys
import asyncio
import ccxt.pro
import pandas as pd
import talib as ta
import numpy as np
from datetime import datetime, timedelta
import requests
import json
from rich.console import Console
from rich.table import Table
from rich.live import Live
import time

# 修复 aiodns 在 Windows 上的兼容性问题
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# === CONFIGURATION ===
CONFIG = {
    'symbols_to_track': ['BTC/USDT', 'ETH/USDT'],
    'timeframe': '4h',
    'scan_interval_seconds': 600,  # 10 minutes, for faster reaction
    'feishu_webhook_url': 'https://open.feishu.cn/open-apis/bot/v2/hook/f9a9508a-2940-4cf8-b629-8e2da21b59e9', # 请替换为你的 Webhook URL

    # --- Phase 1: Purge (卖盘力竭) ---
    # 定义“清洗事件”的阈值
    'purge': {
        'lookback_candles': 48, # ~8 days on 4h timeframe
        'price_drop_pct': 0.05, # 价格在单根K线内下跌超过 5%
        'volume_spike_factor': 3.0, # 成交量是过去均值的 3 倍以上
        'deep_negative_funding_threshold': -0.0003 # 资金费率低于 -0.03%
    },

    # --- Phase 2: Stabilization (多空博弈) ---
    # 定义“稳固”的条件
    'stabilization': {
        'stabilization_candles': 6, # 在探测到清洗后，需要稳定 6 根K线 (24小时)
        'neutral_funding_range': (-0.0002, 0.0002), # 资金费率回归到中性区间
    },
    
    # --- Phase 3: Confirmation (买盘主导) ---
    # 确认信号将基于收复清洗事件发生前的低点
}
# =====================

console = Console()

class BottomDetector:
    """
    一个状态机，用于跟踪和识别单个资产的潜在市场底部。
    遵循三阶段确认法：Purge -> Stabilization -> Confirmation.
    """
    def __init__(self, symbol, timeframe):
        self.symbol = symbol
        self.timeframe = timeframe
        self.reset()
        console.log(f"Detector for [cyan]{self.symbol}[/cyan] initialized.")

    def reset(self):
        """重置状态机到初始状态"""
        self.state = "IDLE"
        self.purge_info = {} # 存储清洗事件的关键信息
        self.stabilization_info = {}

    async def check(self, exchange):
        """主检查函数，按顺序执行三个阶段的逻辑"""
        try:
            # 1. 获取数据
            limit = CONFIG['purge']['lookback_candles'] + 20 # 获取足够的数据
            ohlcv = await exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            
            # 修复资金费率获取方式
            current_funding_rate = 0.0
            try:
                funding_data = await exchange.fetch_funding_rate(self.symbol)
                current_funding_rate = funding_data.get('fundingRate', 0.0)
            except Exception as funding_error:
                # 如果获取资金费率失败，使用默认值0.0
                console.log(f"[yellow]Warning: Could not fetch funding rate for {self.symbol}: {funding_error}[/yellow]")
                current_funding_rate = 0.0

            # 添加技术指标 - 使用 talib 计算布林带
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = ta.BBANDS(df['close'], timeperiod=20)
            df['BBB_20_2.0'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']  # BBW 计算

            # 2. 状态机逻辑
            # 只有在确认后才检查，避免重复触发
            if self.state == "PHASE_2_STABILIZATION_CONFIRMED":
                await self._check_phase_3_confirmation(df)

            # 只有在探测到清洗后才检查是否稳固
            if self.state == "PHASE_1_PURGE_DETECTED":
                await self._check_phase_2_stabilization(df, current_funding_rate)

            # 任何时候都检查是否发生清洗事件
            if self.state == "IDLE":
                await self._check_phase_1_purge(df, current_funding_rate)
            
            return self.get_status()

        except Exception as e:
            console.log(f"[bold red]Error checking {self.symbol}:[/bold red] {e}")
            return self.get_status() # 即使出错也返回当前状态

    def get_status(self):
        """获取当前探测器的状态，用于在控制台显示"""
        return {
            "Symbol": self.symbol,
            "State": self.state,
            "Purge Low": self.purge_info.get('purge_low', 'N/A'),
            "Reclaim Level": self.purge_info.get('reclaim_level', 'N/A'),
            "Detected At": self.purge_info.get('timestamp', 'N/A')
        }

    async def _check_phase_1_purge(self, df, funding_rate):
        """检查是否发生“清洗事件”"""
        cfg = CONFIG['purge']
        # 我们只关心最新已收盘的K线
        last_candle = df.iloc[-2]
        prev_candles = df.iloc[-cfg['lookback_candles']-2:-2]

        price_drop = (last_candle['open'] - last_candle['close']) / last_candle['open']
        avg_volume = prev_candles['volume'].mean()
        
        # 条件判断
        is_price_drop_significant = price_drop > cfg['price_drop_pct']
        is_volume_spike = last_candle['volume'] > avg_volume * cfg['volume_spike_factor']
        is_funding_deep_negative = funding_rate < cfg['deep_negative_funding_threshold']

        if is_price_drop_significant and is_volume_spike and is_funding_deep_negative:
            self.state = "PHASE_1_PURGE_DETECTED"
            self.purge_info = {
                'purge_low': last_candle['low'],
                'reclaim_level': df.iloc[-3]['low'], # 清洗发生前一根K线的低点作为关键收复位
                'purge_volume': last_candle['volume'],
                'avg_volume': avg_volume,
                'price_drop_pct': price_drop,
                'funding_rate': funding_rate,
                'timestamp': last_candle['timestamp'].strftime('%Y-%m-%d %H:%M')
            }
            console.log(f"🔥 [bold yellow]Phase 1 Purge DETECTED[/bold yellow] for {self.symbol} at {self.purge_info['timestamp']}")
            await self.send_alert("Phase 1 Detected", self.purge_info)

    async def _check_phase_2_stabilization(self, df, funding_rate):
        """在清洗后，检查市场是否稳固"""
        cfg = CONFIG['stabilization']
        purge_timestamp = pd.to_datetime(self.purge_info['timestamp'])
        
        # 筛选出清洗事件之后的数据
        candles_since_purge = df[df['timestamp'] > purge_timestamp]
        
        if len(candles_since_purge) < cfg['stabilization_candles']:
            return # 稳定时间不够

        # 条件判断
        has_not_made_new_low = candles_since_purge['low'].min() >= self.purge_info['purge_low']
        is_funding_neutral = cfg['neutral_funding_range'][0] <= funding_rate <= cfg['neutral_funding_range'][1]
        
        # 检查波动率是否收缩
        bbw_col = 'BBB_20_2.0'
        is_bbw_contracting = candles_since_purge.iloc[-1][bbw_col] < candles_since_purge.iloc[0][bbw_col] if bbw_col in df.columns else True

        if has_not_made_new_low and is_funding_neutral and is_bbw_contracting:
            self.state = "PHASE_2_STABILIZATION_CONFIRMED"
            self.stabilization_info = {
                'stabilized_candles': len(candles_since_purge),
                'current_funding': funding_rate,
                'current_bbw': candles_since_purge.iloc[-1][bbw_col]
            }
            console.log(f"⚖️ [bold cyan]Phase 2 Stabilization CONFIRMED[/bold cyan] for {self.symbol}")
            await self.send_alert("Phase 2 Confirmed", {**self.purge_info, **self.stabilization_info})

    async def _check_phase_3_confirmation(self, df):
        """在稳固后，检查是否收复关键位置"""
        last_closed_price = df.iloc[-2]['close']
        reclaim_level = self.purge_info['reclaim_level']

        if last_closed_price > reclaim_level:
            confirmation_info = {
                'reclaimed_price': last_closed_price,
                'reclaim_level': reclaim_level,
                'confirmation_time': df.iloc[-2]['timestamp'].strftime('%Y-%m-%d %H:%M')
            }
            console.log(f"✅ [bold green]Phase 3 BOTTOM CONFIRMED[/bold green] for {self.symbol}!")
            await self.send_alert("🚨 Potential Bottom Confirmed!", {**self.purge_info, **self.stabilization_info, **confirmation_info})
            self.reset() # 触发最终警报后重置状态机

    async def send_alert(self, title, details):
        """发送飞书通知"""
        webhook_url = CONFIG['feishu_webhook_url']
        if not webhook_url or 'YOUR_WEBHOOK_KEY' in webhook_url: return

        details_md = ""
        for key, value in details.items():
            if isinstance(value, float): value = f"{value:.4f}"
            details_md += f"**{key.replace('_', ' ').title()}:** {value}\n"

        card_message = {
            "msg_type": "interactive",
            "card": {
                "header": {
                    "title": {"content": f"📈 {self.symbol} - {title}", "tag": "plain_text"},
                    "template": "blue" if "Phase 1" in title else "cyan" if "Phase 2" in title else "green"
                },
                "elements": [{"tag": "div", "text": {"content": details_md, "tag": "lark_md"}}]
            }
        }
        
        def _blocking_post():
            try:
                requests.post(webhook_url, headers={'Content-Type': 'application/json'}, data=json.dumps(card_message))
            except Exception as e:
                console.log(f"[bold red]Feishu post failed:[/bold red] {e}")
        
        await asyncio.get_running_loop().run_in_executor(None, _blocking_post)


async def test_api_connection(exchange):
    """测试API连接"""
    try:
        # 测试基本的市场数据获取
        ohlcv = await exchange.fetch_ohlcv('BTC/USDT', '1h', limit=5)
        console.log(f"✅ API connection test successful. Got {len(ohlcv)} candles for BTC/USDT")
        return True
    except Exception as e:
        console.log(f"❌ API connection test failed: {e}")
        return False

async def main_loop():
    """主监控循环"""
    exchange = ccxt.pro.binance({
        'enableRateLimit': True,
        'options': {'defaultType': 'future'}  # 使用期货合约，与0728版本保持一致
    })
    
    # 测试API连接
    console.log("Testing API connection...")
    if not await test_api_connection(exchange):
        console.log("[bold red]API connection failed. Please check your network and try again.[/bold red]")
        await exchange.close()
        return
    
    detectors = {
        symbol: BottomDetector(symbol, CONFIG['timeframe'])
        for symbol in CONFIG['symbols_to_track']
    }

    try:
        with Live(console=console, screen=False, auto_refresh=False) as live:
            while True:
                start_time = time.time()
                console.rule(f"[bold yellow]Starting Scan at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 并发检查所有追踪的资产
                tasks = [detector.check(exchange) for detector in detectors.values()]
                statuses = await asyncio.gather(*tasks)
                
                # 更新 rich 表格
                table = Table(title="🔍 Local Bottom Detector Status")
                table.add_column("Symbol", style="cyan")
                table.add_column("State")
                table.add_column("Purge Low")
                table.add_column("Reclaim Level")
                table.add_column("Detected At")
                
                for status in statuses:
                    table.add_row(
                        str(status['Symbol']), 
                        str(status['State']), 
                        str(status['Purge Low']), 
                        str(status['Reclaim Level']), 
                        str(status['Detected At'])
                    )
                
                live.update(table, refresh=True)

                end_time = time.time()
                elapsed = end_time - start_time
                wait_time = max(0, CONFIG['scan_interval_seconds'] - elapsed)
                
                console.log(f"Scan complete in {elapsed:.2f}s. Waiting for {wait_time:.2f}s...")
                await asyncio.sleep(wait_time)

    finally:
        await exchange.close()


if __name__ == "__main__":
    try:
        asyncio.run(main_loop())
    except KeyboardInterrupt:
        console.log("\nMonitor stopped by user.")