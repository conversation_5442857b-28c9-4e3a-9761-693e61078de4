#!/usr/bin/env python3
"""
修复 pandas_ta 与新版本 numpy 的兼容性问题
"""

import os
import sys
import subprocess

def fix_pandas_ta_numpy_compatibility():
    """修复 pandas_ta 库中的 numpy 兼容性问题"""
    
    # 获取 pandas_ta 安装路径
    try:
        import pandas_ta
        pandas_ta_path = os.path.dirname(pandas_ta.__file__)
        print(f"Found pandas_ta at: {pandas_ta_path}")
    except ImportError:
        print("pandas_ta not installed. Please install it first.")
        return False
    
    # 需要修复的文件
    files_to_fix = [
        os.path.join(pandas_ta_path, "momentum", "squeeze_pro.py"),
        # 可能还有其他文件需要修复
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"Fixing {file_path}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换 NaN 为 nan
            if 'from numpy import NaN as npNaN' in content:
                content = content.replace('from numpy import NaN as npNaN', 'from numpy import nan as npNaN')
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ Fixed {file_path}")
            else:
                print(f"⚠️  No fix needed for {file_path}")
        else:
            print(f"❌ File not found: {file_path}")
    
    return True

def reinstall_compatible_versions():
    """重新安装兼容的版本"""
    print("Reinstalling compatible versions...")
    
    commands = [
        "pip uninstall -y numpy pandas-ta",
        "pip install numpy==1.23.5",
        "pip install pandas-ta==0.3.14b0"
    ]
    
    for cmd in commands:
        print(f"Running: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Command failed: {cmd}")
            print(f"Error: {result.stderr}")
            return False
        else:
            print(f"✅ Command succeeded: {cmd}")
    
    return True

if __name__ == "__main__":
    print("🔧 Fixing pandas_ta numpy compatibility issue...")
    
    # 方案1：尝试修复现有安装
    if "--reinstall" in sys.argv:
        success = reinstall_compatible_versions()
    else:
        success = fix_pandas_ta_numpy_compatibility()
    
    if success:
        print("\n✅ Fix completed! Try running your script again.")
        print("If the issue persists, run: python fix_pandas_ta.py --reinstall")
    else:
        print("\n❌ Fix failed. Please try manual installation:")
        print("pip uninstall -y numpy pandas-ta")
        print("pip install numpy==1.23.5")
        print("pip install pandas-ta==0.3.14b0")