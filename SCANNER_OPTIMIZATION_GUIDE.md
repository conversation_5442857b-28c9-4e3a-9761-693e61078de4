# 扫描器优化指南

## 主要改进

### 1. 架构优化
- **配置管理**: 使用 `dataclass` 统一管理所有配置参数
- **缓存系统**: 实现智能缓存，减少重复API调用
- **错误处理**: 增加重试机制和更好的异常处理
- **日志系统**: 使用标准logging模块，便于调试

### 2. 性能优化
- **并发控制**: 优化异步任务的并发度
- **数据缓存**: 缓存BTC数据、相关性矩阵等
- **网络优化**: 增加超时控制和重试机制
- **内存管理**: 及时清理不需要的数据

### 3. 数据质量改进
- **异常值处理**: 更严格的Winsorization处理
- **数据验证**: 确保计算前有足够的数据点
- **动态阈值**: 根据市场条件调整筛选阈值

### 4. 可配置性增强
- **预设配置**: 提供多种交易风格的预设
- **动态调整**: 可根据市场条件切换配置
- **参数验证**: 确保配置参数的合理性

## 使用方法

### 基础使用
```python
# 使用默认配置
from scanner_optimized import run_realtime_monitor
import asyncio

asyncio.run(run_realtime_monitor())
```

### 使用预设配置
```python
from scanner_config import get_config
from scanner_optimized import CONFIG

# 使用保守配置
CONFIG = get_config('conservative')

# 使用激进配置
CONFIG = get_config('aggressive')

# 使用短线配置
CONFIG = get_config('scalping')
```

### 自定义配置
```python
from scanner_config import ScannerConfig

# 创建自定义配置
custom_config = ScannerConfig(
    top_n=100,
    scan_interval_seconds=600,  # 10分钟扫描一次
    trend_strength_threshold=1.2,
    min_volume_threshold=2000000
)
```

## 配置说明

### 核心参数
- `top_n`: 处理的品种数量（建议30-100）
- `scan_interval_seconds`: 扫描间隔（建议300-3600秒）
- `lookback`: 技术指标回看期（建议10-30）

### 策略阈值
- `trend_strength_threshold`: 趋势强度阈值（建议0.8-2.0）
- `trend_adx_threshold`: ADX趋势确认阈值（建议15-35）
- `correlation_threshold`: 配对交易相关性阈值（建议0.2-0.5）

### 缓存设置
- `correlation_cache_hours`: 相关性缓存时间（建议2-8小时）
- `market_data_cache_minutes`: 市场数据缓存时间（建议15-60分钟）

## 性能监控

### 关键指标
1. **扫描完成时间**: 应控制在2-5分钟内
2. **API调用次数**: 通过缓存减少重复调用
3. **内存使用**: 监控内存泄漏
4. **错误率**: 网络请求失败率应低于5%

### 优化建议
1. **网络条件差时**: 增加 `max_retries` 和 `retry_delay`
2. **内存不足时**: 减少 `top_n` 和缓存时间
3. **CPU负载高时**: 增加 `scan_interval_seconds`

## 故障排除

### 常见问题

#### 1. 扫描速度慢
```python
# 解决方案：减少处理品种数量
CONFIG.top_n = 30

# 或增加缓存时间
CONFIG.market_data_cache_minutes = 60
```

#### 2. 网络请求失败
```python
# 解决方案：增加重试次数和延迟
CONFIG.max_retries = 5
CONFIG.retry_delay = 2.0
CONFIG.request_timeout = 60
```

#### 3. 内存使用过高
```python
# 解决方案：减少缓存时间
CONFIG.correlation_cache_hours = 2
CONFIG.market_data_cache_minutes = 15
```

#### 4. 结果不稳定
```python
# 解决方案：增加数据质量控制
CONFIG.min_volume_threshold = 5000000
CONFIG.min_data_points = 50
```

## 监控和日志

### 日志级别
- `INFO`: 正常运行信息
- `WARNING`: 非致命错误
- `ERROR`: 严重错误
- `DEBUG`: 详细调试信息

### 关键日志
- 扫描开始/结束时间
- 处理的品种数量
- 缓存命中率
- API调用失败次数

## 扩展功能

### 1. 添加新的技术指标
```python
# 在 calculate_strength_factors 函数中添加
df.ta.macd(append=True)  # 添加MACD
macd_col = 'MACD_12_26_9'
macd = df[macd_col].iloc[-2] if macd_col in df.columns else 0
```

### 2. 自定义筛选条件
```python
# 在显示函数中添加自定义筛选
custom_filter = (df['rsi'] > 70) | (df['rsi'] < 30)  # RSI超买超卖
filtered_df = df[custom_filter]
```

### 3. 添加新的通知渠道
```python
async def send_telegram_notification(df, correlation_matrix):
    # 实现Telegram通知
    pass

async def send_email_notification(df, correlation_matrix):
    # 实现邮件通知
    pass
```

## 最佳实践

1. **定期检查配置**: 根据市场变化调整参数
2. **监控性能**: 关注扫描时间和资源使用
3. **备份数据**: 定期保存扫描结果
4. **测试配置**: 在模拟环境中测试新配置
5. **渐进优化**: 逐步调整参数，观察效果

## 版本更新

### v2.0 (优化版)
- 重构代码架构
- 增加配置管理
- 优化性能和稳定性
- 增强错误处理

### 未来计划
- 机器学习模型集成
- 更多技术指标支持
- 实时风险管理
- 策略回测功能