# WebSocket连接问题彻底修复方案

## 问题分析

根据您的日志，WebSocket连接失败的原因可能包括：

1. **网络连接问题**：代理配置冲突
2. **参数兼容性问题**：websockets库版本差异
3. **连接超时问题**：网络延迟或服务器响应慢
4. **币安服务器问题**：临时性的服务不可用

## 解决方案

### 方案1：简化连接参数（推荐）

将WebSocket连接部分替换为以下代码：

```python
# 在run方法中的WebSocket连接部分
try:
    stream_url = self.websocket_base_url + f"/ws/{self.listen_key}"
    logger.info(f"正在连接WebSocket: {stream_url}")
    
    # 简化的连接参数，提高兼容性
    async with websockets.connect(
        stream_url,
        ping_interval=20,
        ping_timeout=10,
        close_timeout=10
    ) as websocket:
        logger.info("✅ WebSocket 连接成功")
        connection_failures = 0  # 重置失败计数
        
        # 订阅ticker数据流
        ticker_subscribe = {
            "method": "SUBSCRIBE",
            "params": [f"{self.symbol.lower()}@ticker"],
            "id": 1
        }
        await websocket.send(json.dumps(ticker_subscribe))
        logger.info(f"已订阅 {self.symbol} ticker数据流")

        # 订阅用户数据流
        await websocket.send(json.dumps({"method": "SUBSCRIBE", "params": [], "id": 2}))
        logger.info("已订阅用户数据流")

        # 消息处理循环
        async for message in websocket:
            try:
                data = json.loads(message)
                
                # 处理ticker数据
                if 'stream' in data and '@ticker' in data['stream']:
                    await self.handle_ticker_update(data['data'])
                
                # 处理用户数据
                elif 'e' in data:
                    if data['e'] == 'ORDER_TRADE_UPDATE':
                        await self.handle_order_update(data['o'])
                    elif data['e'] == 'ACCOUNT_UPDATE':
                        await self.handle_account_update(data['a'])
                        
            except json.JSONDecodeError:
                logger.warning(f"无法解析WebSocket消息: {message}")
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {e}")

except websockets.exceptions.ConnectionClosed as e:
    logger.warning(f"WebSocket连接已关闭: {e}")
except websockets.exceptions.InvalidURI as e:
    logger.error(f"WebSocket URI无效: {e}")
except websockets.exceptions.InvalidHandshake as e:
    logger.error(f"WebSocket握手失败: {e}")
except Exception as e:
    connection_failures += 1
    logger.error(f"WebSocket连接失败 ({connection_failures}/10): {e}")
```

### 方案2：添加重连机制

```python
import asyncio
from websockets.exceptions import ConnectionClosed, InvalidURI, InvalidHandshake

async def connect_websocket_with_retry(self):
    """带重试机制的WebSocket连接"""
    max_retries = 10
    retry_count = 0
    base_delay = 5
    
    while retry_count < max_retries and not self.is_shutting_down:
        try:
            # 获取新的ListenKey
            self.listen_key = await self.fetch_listen_key()
            stream_url = self.websocket_base_url + f"/ws/{self.listen_key}"
            
            logger.info(f"尝试连接WebSocket ({retry_count + 1}/{max_retries}): {stream_url}")
            
            # 连接WebSocket
            async with websockets.connect(
                stream_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                logger.info("✅ WebSocket连接成功")
                retry_count = 0  # 重置重试计数
                
                # 订阅数据流
                await self.subscribe_streams(websocket)
                
                # 处理消息
                await self.handle_websocket_messages(websocket)
                
        except (ConnectionClosed, InvalidURI, InvalidHandshake) as e:
            retry_count += 1
            delay = min(base_delay * (2 ** retry_count), 300)  # 最大5分钟
            logger.error(f"WebSocket连接失败: {e}")
            logger.info(f"等待 {delay} 秒后重连...")
            await asyncio.sleep(delay)
            
        except Exception as e:
            retry_count += 1
            delay = min(base_delay * retry_count, 60)
            logger.error(f"WebSocket连接异常: {e}")
            logger.info(f"等待 {delay} 秒后重连...")
            await asyncio.sleep(delay)
    
    if retry_count >= max_retries:
        logger.critical("WebSocket连接重试次数已达上限，程序退出")
        self.is_shutting_down = True

async def subscribe_streams(self, websocket):
    """订阅数据流"""
    # 订阅ticker数据流
    ticker_subscribe = {
        "method": "SUBSCRIBE",
        "params": [f"{self.symbol.lower()}@ticker"],
        "id": 1
    }
    await websocket.send(json.dumps(ticker_subscribe))
    logger.info(f"已订阅 {self.symbol} ticker数据流")

    # 订阅用户数据流
    await websocket.send(json.dumps({"method": "SUBSCRIBE", "params": [], "id": 2}))
    logger.info("已订阅用户数据流")

async def handle_websocket_messages(self, websocket):
    """处理WebSocket消息"""
    try:
        async for message in websocket:
            try:
                data = json.loads(message)
                
                # 处理ticker数据
                if 'stream' in data and '@ticker' in data['stream']:
                    await self.handle_ticker_update(data['data'])
                
                # 处理用户数据
                elif 'e' in data:
                    if data['e'] == 'ORDER_TRADE_UPDATE':
                        await self.handle_order_update(data['o'])
                    elif data['e'] == 'ACCOUNT_UPDATE':
                        await self.handle_account_update(data['a'])
                        
            except json.JSONDecodeError:
                logger.warning(f"无法解析WebSocket消息: {message}")
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {e}")
                
    except ConnectionClosed:
        logger.warning("WebSocket连接已关闭，准备重连")
        raise  # 重新抛出异常以触发重连
```

### 方案3：网络诊断工具

创建网络诊断脚本 `network_test.py`：

```python
import asyncio
import websockets
import json
import time

async def test_binance_websocket():
    """测试币安WebSocket连接"""
    test_url = "wss://fstream.binance.com/ws/btcusdt@ticker"
    
    try:
        print("测试币安WebSocket连接...")
        async with websockets.connect(test_url, ping_interval=20) as websocket:
            print("✅ 连接成功")
            
            # 等待几条消息
            for i in range(3):
                message = await websocket.recv()
                data = json.loads(message)
                print(f"收到消息 {i+1}: {data.get('s', 'N/A')} 价格: {data.get('c', 'N/A')}")
                
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def test_network_latency():
    """测试网络延迟"""
    import aiohttp
    
    try:
        start_time = time.time()
        async with aiohttp.ClientSession() as session:
            async with session.get('https://fapi.binance.com/fapi/v1/ping') as response:
                latency = (time.time() - start_time) * 1000
                print(f"币安API延迟: {latency:.2f}ms")
                
    except Exception as e:
        print(f"网络测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_binance_websocket())
    asyncio.run(test_network_latency())
```

## 实施步骤

1. **备份当前代码**
   ```bash
   cp fuli_BN_V3_cloud.py fuli_BN_V3_cloud.py.backup
   ```

2. **运行网络诊断**
   ```bash
   python network_test.py
   ```

3. **应用修复方案**
   - 选择方案1（简化参数）作为主要修复
   - 如果仍有问题，应用方案2（重连机制）

4. **测试连接**
   - 启动策略，观察WebSocket连接日志
   - 确认连接稳定性

## 预期效果

修复后应该看到：
```
2025-08-04 00:30:00 - INFO - [run:804] - 正在连接WebSocket: wss://fstream.binance.com/ws/...
2025-08-04 00:30:01 - INFO - [run:821] - ✅ WebSocket连接成功
2025-08-04 00:30:01 - INFO - [run:835] - 已订阅 BTC/USDT:USDT ticker数据流
2025-08-04 00:30:01 - INFO - [run:839] - 已订阅用户数据流
```

## 如果问题仍然存在

1. **检查网络环境**
   - 确认代理设置正确
   - 测试直连（临时禁用代理）

2. **更新依赖库**
   ```bash
   pip install --upgrade websockets aiohttp
   ```

3. **联系技术支持**
   - 提供完整的错误日志
   - 说明网络环境配置

---

这个修复方案应该能彻底解决WebSocket连接问题。如果您需要我直接修改代码文件，请告诉我！
