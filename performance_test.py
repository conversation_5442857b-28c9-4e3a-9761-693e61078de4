#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ARGM策略性能对比测试脚本
对比原版本和优化版本的性能差异
"""

import time
import asyncio
import sys
import os
import logging
from typing import Dict, Any
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        self.test_results = {}
        
    def measure_memory(self) -> float:
        """简化的内存测量"""
        return 0.0  # 简化版本，不测量实际内存
    
    def measure_cpu(self) -> float:
        """简化的CPU测量"""
        return 0.0  # 简化版本，不测量实际CPU
    
    async def test_grid_calculation_performance(self):
        """测试网格计算性能"""
        logger.info("测试网格计算性能...")
        
        # 测试原始Python循环方法
        start_time = time.perf_counter()
        start_memory = self.measure_memory()
        
        # 模拟原始网格计算
        high, low, count = 120800.0, 115800.0, 40
        original_grids = []
        step = (high - low) / (count - 1)
        for i in range(count):
            original_grids.append(low + i * step)
        
        original_time = time.perf_counter() - start_time
        original_memory = self.measure_memory() - start_memory
        
        # 测试优化的numpy方法
        start_time = time.perf_counter()
        start_memory = self.measure_memory()
        
        optimized_grids = np.linspace(low, high, count, dtype=np.float64)
        
        optimized_time = time.perf_counter() - start_time
        optimized_memory = self.measure_memory() - start_memory
        
        # 计算性能提升
        time_improvement = (original_time - optimized_time) / original_time * 100
        memory_improvement = (original_memory - optimized_memory) / max(original_memory, 0.001) * 100
        
        self.test_results['grid_calculation'] = {
            'original_time_ms': original_time * 1000,
            'optimized_time_ms': optimized_time * 1000,
            'time_improvement_pct': time_improvement,
            'original_memory_mb': original_memory,
            'optimized_memory_mb': optimized_memory,
            'memory_improvement_pct': memory_improvement
        }
        
        logger.info(f"网格计算优化: 时间提升 {time_improvement:.1f}%, 内存优化 {memory_improvement:.1f}%")
    
    async def test_data_structure_performance(self):
        """测试数据结构性能"""
        logger.info("测试数据结构性能...")
        
        # 测试原始字典结构
        start_time = time.perf_counter()
        start_memory = self.measure_memory()
        
        original_orders = []
        for i in range(1000):
            order = {
                'id': f'order_{i}',
                'price': 100.0 + i,
                'quantity': 0.01,
                'side': 'buy' if i % 2 == 0 else 'sell',
                'status': 'open',
                'timestamp': time.time()
            }
            original_orders.append(order)
        
        original_time = time.perf_counter() - start_time
        original_memory = self.measure_memory() - start_memory
        
        # 测试优化的dataclass结构
        from dataclasses import dataclass
        
        @dataclass
        class OptimizedOrder:
            __slots__ = ['id', 'price', 'quantity', 'side', 'status', 'timestamp']
            id: str
            price: float
            quantity: float
            side: str
            status: str
            timestamp: float
        
        start_time = time.perf_counter()
        start_memory = self.measure_memory()
        
        optimized_orders = []
        for i in range(1000):
            order = OptimizedOrder(
                id=f'order_{i}',
                price=100.0 + i,
                quantity=0.01,
                side='buy' if i % 2 == 0 else 'sell',
                status='open',
                timestamp=time.time()
            )
            optimized_orders.append(order)
        
        optimized_time = time.perf_counter() - start_time
        optimized_memory = self.measure_memory() - start_memory
        
        # 计算性能提升
        time_improvement = (original_time - optimized_time) / original_time * 100
        memory_improvement = (original_memory - optimized_memory) / max(original_memory, 0.001) * 100
        
        self.test_results['data_structure'] = {
            'original_time_ms': original_time * 1000,
            'optimized_time_ms': optimized_time * 1000,
            'time_improvement_pct': time_improvement,
            'original_memory_mb': original_memory,
            'optimized_memory_mb': optimized_memory,
            'memory_improvement_pct': memory_improvement
        }
        
        logger.info(f"数据结构优化: 时间提升 {time_improvement:.1f}%, 内存优化 {memory_improvement:.1f}%")
    
    async def test_async_performance(self):
        """测试异步处理性能"""
        logger.info("测试异步处理性能...")
        
        # 模拟串行处理
        start_time = time.perf_counter()
        
        async def slow_operation(delay=0.001):
            await asyncio.sleep(delay)
            return delay
        
        # 串行执行
        results = []
        for i in range(100):
            result = await slow_operation(0.001)
            results.append(result)
        
        serial_time = time.perf_counter() - start_time
        
        # 并行执行
        start_time = time.perf_counter()
        
        tasks = [slow_operation(0.001) for _ in range(100)]
        results = await asyncio.gather(*tasks)
        
        parallel_time = time.perf_counter() - start_time
        
        # 计算性能提升
        time_improvement = (serial_time - parallel_time) / serial_time * 100
        
        self.test_results['async_processing'] = {
            'serial_time_ms': serial_time * 1000,
            'parallel_time_ms': parallel_time * 1000,
            'time_improvement_pct': time_improvement
        }
        
        logger.info(f"异步处理优化: 时间提升 {time_improvement:.1f}%")
    
    def generate_report(self) -> str:
        """生成性能测试报告"""
        report = """
╔══════════════════════════════════════════════════════════════╗
║                    ARGM策略性能测试报告                       ║
╠══════════════════════════════════════════════════════════════╣
"""
        
        for test_name, results in self.test_results.items():
            report += f"║ 📊 {test_name.replace('_', ' ').title()}:\n"
            
            if 'original_time_ms' in results:
                report += f"║   • 原始耗时: {results['original_time_ms']:.2f}ms\n"
                report += f"║   • 优化耗时: {results['optimized_time_ms']:.2f}ms\n"
                report += f"║   • 时间提升: {results['time_improvement_pct']:.1f}%\n"
            
            if 'original_memory_mb' in results:
                report += f"║   • 原始内存: {results['original_memory_mb']:.2f}MB\n"
                report += f"║   • 优化内存: {results['optimized_memory_mb']:.2f}MB\n"
                report += f"║   • 内存优化: {results['memory_improvement_pct']:.1f}%\n"
            
            if 'serial_time_ms' in results:
                report += f"║   • 串行耗时: {results['serial_time_ms']:.2f}ms\n"
                report += f"║   • 并行耗时: {results['parallel_time_ms']:.2f}ms\n"
                report += f"║   • 时间提升: {results['time_improvement_pct']:.1f}%\n"
            
            report += "║\n"
        
        report += """╚══════════════════════════════════════════════════════════════╝
        """
        
        return report

async def main():
    """主测试函数"""
    logger.info("开始ARGM策略性能测试...")
    
    tester = PerformanceTester()
    
    # 执行各项性能测试
    await tester.test_grid_calculation_performance()
    await tester.test_data_structure_performance()
    await tester.test_async_performance()
    
    # 生成并显示报告
    report = tester.generate_report()
    print(report)
    
    # 保存报告到文件
    with open('performance_test_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    logger.info("性能测试完成，报告已保存到 performance_test_report.txt")

if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    asyncio.run(main())