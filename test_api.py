#!/usr/bin/env python3
"""
简单的API连接测试
"""

import asyncio
import ccxt.pro
import sys

# Windows 兼容性
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

async def test_binance_api():
    """测试币安API连接"""
    
    # 测试不同的配置
    configs = [
        {'enableRateLimit': True, 'options': {'defaultType': 'future'}},
        {'enableRateLimit': True, 'options': {'defaultType': 'swap'}},
        {'enableRateLimit': True},
    ]
    
    for i, config in enumerate(configs):
        print(f"\n=== 测试配置 {i+1}: {config} ===")
        
        exchange = ccxt.pro.binance(config)
        
        try:
            # 测试获取市场信息
            print("1. 测试获取市场信息...")
            markets = await exchange.load_markets()
            print(f"✅ 成功获取 {len(markets)} 个市场")
            
            # 测试获取OHLCV数据
            print("2. 测试获取OHLCV数据...")
            ohlcv = await exchange.fetch_ohlcv('BTC/USDT', '1h', limit=5)
            print(f"✅ 成功获取 {len(ohlcv)} 根K线数据")
            
            # 测试获取资金费率
            print("3. 测试获取资金费率...")
            try:
                funding = await exchange.fetch_funding_rate('BTC/USDT')
                print(f"✅ 成功获取资金费率: {funding.get('fundingRate', 'N/A')}")
            except Exception as e:
                print(f"⚠️  资金费率获取失败: {e}")
            
            print(f"✅ 配置 {i+1} 测试成功!")
            await exchange.close()
            return config  # 返回成功的配置
            
        except Exception as e:
            print(f"❌ 配置 {i+1} 测试失败: {e}")
            await exchange.close()
            continue
    
    return None

async def main():
    print("开始测试币安API连接...")
    
    successful_config = await test_binance_api()
    
    if successful_config:
        print(f"\n🎉 找到可用配置: {successful_config}")
    else:
        print("\n😞 所有配置都失败了")

if __name__ == "__main__":
    asyncio.run(main())