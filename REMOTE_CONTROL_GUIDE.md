# Remote Control 远程控制配置指南

## 概述

Remote Control功能允许您通过HTTP API远程监控和控制策略，包括：
- 实时查看策略状态
- 远程暂停/恢复策略
- 紧急停止策略
- 获取详细的运行数据

## 1. 代码配置

### 1.1 启用Remote Control

在 `fuli_BN_V3_cloud.py` 的 `CONFIG` 中修改：

```python
"remote_control": {
    "enabled": True,                     # ❗ 改为 True 启用
    "host": "127.0.0.1",                # 服务器监听地址
    "port": 8888,                       # 服务器端口
    "auth_token": "your_secret_token_123", # ❗ 修改为安全的令牌
},
```

### 1.2 安全配置建议

```python
"remote_control": {
    "enabled": True,
    "host": "0.0.0.0",                  # 允许外网访问（谨慎使用）
    "port": 8888,                       
    "auth_token": "MySecureToken2024!@#", # 使用强密码
},
```

**⚠️ 安全提示：**
- `host`: 
  - `127.0.0.1` = 仅本机访问
  - `0.0.0.0` = 允许外网访问（需要防火墙保护）
- `auth_token`: 必须修改为强密码，避免使用默认值

## 2. API接口使用

### 2.1 获取策略状态

```bash
curl -H "Authorization: Bearer your_secret_token_123" \
     http://127.0.0.1:8888/status
```

**响应示例：**
```json
{
  "timestamp": "2025-08-04T00:30:00",
  "strategy_status": "running",
  "symbol": "BTC/USDT:USDT",
  "current_price": 98500.0,
  "equity": {
    "initial": 1000.0,
    "current": 1025.5,
    "pnl": 25.5,
    "peak": 1030.0
  },
  "positions": {
    "long": 0.0025,
    "short": 0.0
  },
  "grid_stats": {
    "total_pairs": 40,
    "buy_placed": 15,
    "sell_placed": 8,
    "empty": 17
  },
  "completed_cycles": 12,
  "open_orders": 23
}
```

### 2.2 暂停策略

```bash
curl -X POST \
     -H "Authorization: Bearer your_secret_token_123" \
     -H "Content-Type: application/json" \
     -d '{"duration": 3600}' \
     http://127.0.0.1:8888/pause
```

**参数说明：**
- `duration`: 暂停时长（秒），默认3600秒（1小时）

### 2.3 恢复策略

```bash
curl -X POST \
     -H "Authorization: Bearer your_secret_token_123" \
     http://127.0.0.1:8888/resume
```

### 2.4 紧急停止策略

```bash
curl -X POST \
     -H "Authorization: Bearer your_secret_token_123" \
     http://127.0.0.1:8888/stop
```

## 3. 飞书集成控制

### 3.1 创建飞书应用（推荐方式）

1. **登录飞书开放平台**
   - 访问：https://open.feishu.cn/
   - 使用飞书账号登录

2. **创建应用**
   - 点击"创建应用" → "自建应用"
   - 填写应用名称：如"交易策略控制"
   - 选择应用类型：企业自建应用

3. **配置机器人**
   - 进入应用 → "机器人" → "启用机器人"
   - 设置机器人名称和描述
   - 获取 Webhook URL

4. **添加到群组**
   - 在飞书群组中 @机器人名称
   - 或直接邀请机器人进群

### 3.2 创建自定义机器人（简单方式）

1. **在飞书群组中添加机器人**
   - 打开飞书群组
   - 点击群设置 → "群机器人" → "添加机器人"
   - 选择"自定义机器人"

2. **配置机器人**
   - 机器人名称：交易策略监控
   - 描述：监控和控制交易策略
   - 复制 Webhook URL

3. **更新代码配置**
   ```python
   "feishu": {
       "webhook_url": "https://open.feishu.cn/open-apis/bot/v2/hook/你的webhook地址",
   }
   ```

### 3.3 飞书消息卡片控制（高级）

创建带按钮的消息卡片，实现一键控制：

```python
# 在FeishuNotifier类中添加控制卡片方法
async def send_control_card(self):
    """发送带控制按钮的消息卡片"""
    card = {
        "msg_type": "interactive",
        "card": {
            "header": {
                "title": {"tag": "plain_text", "content": "策略控制面板"},
                "template": "blue"
            },
            "elements": [
                {
                    "tag": "action",
                    "actions": [
                        {
                            "tag": "button",
                            "text": {"tag": "plain_text", "content": "暂停策略"},
                            "type": "danger",
                            "url": f"http://your-server:8888/pause?token={self.auth_token}"
                        },
                        {
                            "tag": "button", 
                            "text": {"tag": "plain_text", "content": "恢复策略"},
                            "type": "primary",
                            "url": f"http://your-server:8888/resume?token={self.auth_token}"
                        }
                    ]
                }
            ]
        }
    }
    await self.send_message("", "", card)
```

## 4. 使用场景示例

### 4.1 监控脚本

创建 `monitor.py` 定期检查策略状态：

```python
import requests
import time

def check_strategy_status():
    try:
        response = requests.get(
            "http://127.0.0.1:8888/status",
            headers={"Authorization": "Bearer your_secret_token_123"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"策略状态: {data['strategy_status']}")
            print(f"当前盈亏: {data['equity']['pnl']:.2f} USDT")
            print(f"完成周期: {data['completed_cycles']}")
        else:
            print(f"API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"监控失败: {e}")

# 每分钟检查一次
while True:
    check_strategy_status()
    time.sleep(60)
```

### 4.2 自动风控脚本

```python
import requests

def emergency_stop_if_loss():
    response = requests.get(
        "http://127.0.0.1:8888/status",
        headers={"Authorization": "Bearer your_secret_token_123"}
    )
    
    if response.status_code == 200:
        data = response.json()
        pnl_pct = data['equity']['pnl'] / data['equity']['initial']
        
        # 如果亏损超过10%，紧急停止
        if pnl_pct < -0.10:
            requests.post(
                "http://127.0.0.1:8888/stop",
                headers={"Authorization": "Bearer your_secret_token_123"}
            )
            print("⚠️ 触发紧急停止：亏损超过10%")
```

## 5. 故障排除

### 5.1 常见问题

**Q: 无法访问API接口**
- 检查 `enabled: True`
- 确认端口8888未被占用
- 检查防火墙设置

**Q: 认证失败**
- 确认 `auth_token` 配置正确
- 检查请求头格式：`Authorization: Bearer token`

**Q: 飞书通知失败**
- 验证 webhook URL 是否正确
- 检查机器人是否在群组中
- 确认网络连接正常

### 5.2 调试方法

1. **查看日志**
   ```bash
   tail -f log/fuli_BN_V3_cloud.log | grep "远程控制"
   ```

2. **测试API连通性**
   ```bash
   curl -v http://127.0.0.1:8888/status
   ```

3. **检查端口占用**
   ```bash
   netstat -an | grep 8888
   ```

## 6. 安全建议

1. **网络安全**
   - 仅在可信网络环境使用
   - 配置防火墙限制访问IP
   - 使用HTTPS（生产环境）

2. **认证安全**
   - 定期更换 auth_token
   - 使用强密码（包含字母、数字、特殊字符）
   - 不要在日志中记录token

3. **监控安全**
   - 记录API访问日志
   - 设置异常访问告警
   - 定期检查访问记录

---

**配置完成！** 🎉

现在您可以通过HTTP API和飞书远程监控和控制您的交易策略了。
