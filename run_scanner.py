#!/usr/bin/env python3
"""
扫描器启动脚本
支持不同的配置预设和命令行参数
"""

import asyncio
import argparse
import sys
from rich.console import Console
from rich.table import Table

# 导入扫描器模块
from scanner_config import get_config, list_available_configs, ScannerConfig
import scanner_optimized

console = Console()

def show_config_info(config_name: str):
    """显示配置信息"""
    config = get_config(config_name)
    
    table = Table(title=f"配置信息: {config_name}", style="default")
    table.add_column("参数", style="cyan")
    table.add_column("值", style="green")
    
    table.add_row("处理品种数", str(config.top_n))
    table.add_row("扫描间隔", f"{config.scan_interval_seconds}秒")
    table.add_row("时间周期", ", ".join(config.timeframes))
    table.add_row("趋势强度阈值", str(config.trend_strength_threshold))
    table.add_row("ADX阈值", str(config.trend_adx_threshold))
    table.add_row("相关性阈值", str(config.correlation_threshold))
    table.add_row("最大重试次数", str(config.max_retries))
    
    console.print(table)

def main():
    parser = argparse.ArgumentParser(description="加密货币市场扫描器")
    parser.add_argument(
        "--config", 
        choices=list_available_configs(),
        default="default",
        help="选择配置预设"
    )
    parser.add_argument(
        "--list-configs",
        action="store_true",
        help="列出所有可用配置"
    )
    parser.add_argument(
        "--show-config",
        action="store_true",
        help="显示当前配置详情"
    )
    parser.add_argument(
        "--top-n",
        type=int,
        help="覆盖处理品种数量"
    )
    parser.add_argument(
        "--interval",
        type=int,
        help="覆盖扫描间隔（秒）"
    )
    parser.add_argument(
        "--webhook",
        type=str,
        help="飞书Webhook URL"
    )
    
    args = parser.parse_args()
    
    # 列出配置
    if args.list_configs:
        console.print("[bold]可用配置预设:[/bold]")
        configs_table = Table()
        configs_table.add_column("配置名", style="cyan")
        configs_table.add_column("描述", style="white")
        
        descriptions = {
            'default': '默认配置，平衡的参数设置',
            'conservative': '保守配置，更严格的筛选条件',
            'aggressive': '激进配置，更宽松的筛选条件',
            'scalping': '短线配置，适合高频交易',
            'swing': '波段配置，适合中长线交易',
            'bull_market': '牛市配置，偏向多头机会',
            'bear_market': '熊市配置，偏向空头和防守',
            'sideways_market': '震荡市配置，偏向网格交易'
        }
        
        for config_name in list_available_configs():
            configs_table.add_row(
                config_name, 
                descriptions.get(config_name, "自定义配置")
            )
        
        console.print(configs_table)
        return
    
    # 获取配置
    config = get_config(args.config)
    
    # 应用命令行覆盖
    if args.top_n:
        config.top_n = args.top_n
    if args.interval:
        config.scan_interval_seconds = args.interval
    if args.webhook:
        config.feishu_webhook_url = args.webhook
    
    # 显示配置信息
    if args.show_config:
        show_config_info(args.config)
        return
    
    # 应用配置到扫描器
    scanner_optimized.CONFIG = config
    scanner_optimized.FACTOR_WEIGHTS = config.factor_weights
    
    # 显示启动信息
    console.print(f"[bold green]启动扫描器[/bold green] - 配置: {args.config}")
    console.print(f"处理品种: {config.top_n}")
    console.print(f"扫描间隔: {config.scan_interval_seconds}秒")
    console.print(f"时间周期: {', '.join(config.timeframes)}")
    
    if config.feishu_webhook_url:
        console.print("[green]✓[/green] 飞书通知已启用")
    else:
        console.print("[yellow]⚠[/yellow] 飞书通知未配置")
    
    console.print("\n按 Ctrl+C 停止扫描器\n")
    
    # 启动扫描器
    try:
        asyncio.run(scanner_optimized.run_realtime_monitor())
    except KeyboardInterrupt:
        console.print("\n[yellow]扫描器已停止[/yellow]")
    except Exception as e:
        console.print(f"\n[bold red]扫描器异常退出: {e}[/bold red]")
        sys.exit(1)

if __name__ == "__main__":
    main()