# Fix for pandas_ta compatibility with newer NumPy versions
import numpy as np
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

import sys
# 修复 aiodns 在 Windows 上的兼容性问题
if sys.platform == 'win32':
    import asyncio
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

import ccxt.pro
import asyncio
import pandas as pd
import pandas_ta as ta
import time
from datetime import datetime, timedelta
import requests
import json
from rich.console import Console
from rich.table import Table
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn
import logging
from typing import Dict, List, Optional, Tuple
import aiohttp
from dataclasses import dataclass

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ScanConfig:
    """扫描配置类"""
    timeframes: List[str] = None
    timeframe_weights: List[float] = None
    lookback: int = 20
    top_n: int = 50  # 实际处理的数量
    scan_interval_seconds: int = 1800
    feishu_webhook_url: str = 'https://open.feishu.cn/open-apis/bot/v2/hook/f9a9508a-2940-4cf8-b629-8e2da21b59e9'
    
    # 动态阈值配置
    trend_strength_threshold: float = 1.0
    trend_adx_threshold: float = 25
    range_strength_range: Tuple[float, float] = (-0.5, 0.5)
    range_adx_threshold: float = 20
    correlation_threshold: float = 0.3
    
    # 缓存配置
    correlation_cache_hours: int = 4  # 相关性缓存4小时
    market_data_cache_minutes: int = 30  # 市场数据缓存30分钟
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    def __post_init__(self):
        if self.timeframes is None:
            self.timeframes = ['4h', '1d']
        if self.timeframe_weights is None:
            self.timeframe_weights = [0.6, 0.4]

# 全局配置
CONFIG = ScanConfig()

# 因子权重配置
FACTOR_WEIGHTS = {
    'rel_strength_z': 0.5,
    'volume_24h_z': 0.3,
    'funding_rate_z': -0.2,
}

console = Console()

class DataCache:
    """数据缓存管理器"""
    def __init__(self):
        self.cache = {}
        self.timestamps = {}
    
    def get(self, key: str, max_age_minutes: int = 30) -> Optional[any]:
        """获取缓存数据"""
        if key not in self.cache:
            return None
        
        age = datetime.now() - self.timestamps[key]
        if age.total_seconds() > max_age_minutes * 60:
            del self.cache[key]
            del self.timestamps[key]
            return None
        
        return self.cache[key]
    
    def set(self, key: str, value: any):
        """设置缓存数据"""
        self.cache[key] = value
        self.timestamps[key] = datetime.now()

# 全局缓存实例
cache = DataCache()

async def retry_async(func, *args, max_retries=3, delay=1.0, **kwargs):
    """异步重试装饰器"""
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            logger.warning(f"Attempt {attempt + 1} failed: {e}, retrying in {delay}s...")
            await asyncio.sleep(delay)

async def fetch_perpetual_symbols(exchange) -> List[str]:
    """获取永续合约品种列表（带缓存）"""
    cache_key = "perpetual_symbols"
    cached_symbols = cache.get(cache_key, max_age_minutes=CONFIG.market_data_cache_minutes)
    
    if cached_symbols:
        logger.info(f"Using cached perpetual symbols: {len(cached_symbols)} symbols")
        return cached_symbols
    
    try:
        markets = await retry_async(exchange.load_markets)
        perpetuals = [
            s for s, m in markets.items()
            if m.get('swap') and m.get('active') and 
               m.get('settle') == 'USDT' and m.get('quote') == 'USDT'
        ]
        
        cache.set(cache_key, perpetuals)
        console.log(f"Found [bold green]{len(perpetuals)}[/bold green] active USDT-margined perpetual contracts.")
        return perpetuals
        
    except Exception as e:
        logger.error(f"Error fetching markets: {e}")
        return []

async def get_top_volume_symbols(exchange, all_symbols: List[str], top_n: int) -> List[str]:
    """获取成交量最大的N个品种（带缓存）"""
    cache_key = f"top_volume_symbols_{top_n}"
    cached_symbols = cache.get(cache_key, max_age_minutes=CONFIG.market_data_cache_minutes)
    
    if cached_symbols:
        logger.info(f"Using cached top volume symbols: {len(cached_symbols)} symbols")
        return cached_symbols
    
    try:
        all_tickers = await retry_async(exchange.fetch_tickers)
        volume_data = []
        
        for symbol in all_symbols:
            if symbol in all_tickers and all_tickers[symbol].get('quoteVolume'):
                volume_data.append({
                    'symbol': symbol,
                    'quoteVolume': all_tickers[symbol]['quoteVolume']
                })
        
        if not volume_data:
            logger.error("Could not fetch volume data for any symbol")
            return []
        
        volume_df = pd.DataFrame(volume_data)
        top_symbols = volume_df.sort_values('quoteVolume', ascending=False).head(top_n)['symbol'].tolist()
        
        cache.set(cache_key, top_symbols)
        console.log(f"Selected top {len(top_symbols)} symbols by 24h volume")
        return top_symbols
        
    except Exception as e:
        logger.error(f"Error fetching top volume symbols: {e}")
        return all_symbols[:top_n]  # 降级处理

async def calculate_strength_factors(exchange, symbol: str, timeframes: List[str], 
                                   lookback: int, btc_data_cache: Dict) -> Optional[Dict]:
    """计算强度因子（优化版）"""
    all_timeframe_results = []
    
    for timeframe in timeframes:
        try:
            # 使用重试机制获取数据
            ohlcv = await retry_async(
                exchange.fetch_ohlcv, 
                symbol, timeframe, 
                limit=lookback + 100,
                max_retries=CONFIG.max_retries,
                delay=CONFIG.retry_delay
            )
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            if len(df) < lookback + 27:
                continue
            
            # 技术指标计算
            df.ta.rsi(append=True)
            df.ta.ema(length=20, append=True)
            df.ta.adx(length=lookback, append=True)
            df.ta.bbands(length=lookback, append=True)
            
            # 获取指标列名
            rsi_col = 'RSI_14'
            ema_col = 'EMA_20'
            adx_col = f'ADX_{lookback}'
            bbw_col = f'BBB_{lookback}_2.0'
            
            # 使用已收盘K线数据（避免前瞻性偏差）
            idx = -2
            adx = df[adx_col].iloc[idx] if adx_col in df.columns and pd.notna(df[adx_col].iloc[idx]) else 0
            bbw = df[bbw_col].iloc[idx] if bbw_col in df.columns and pd.notna(df[bbw_col].iloc[idx]) else 0
            rsi = df[rsi_col].iloc[idx] if rsi_col in df.columns and pd.notna(df[rsi_col].iloc[idx]) else 50
            ema_20 = df[ema_col].iloc[idx] if ema_col in df.columns and pd.notna(df[ema_col].iloc[idx]) else 0
            
            # 突破状态计算
            recent_high = df['high'].rolling(window=lookback).max().iloc[idx]
            recent_low = df['low'].rolling(window=lookback).min().iloc[idx]
            current_close = df['close'].iloc[idx]
            
            is_breaking_up = current_close > recent_high
            is_breaking_down = current_close < recent_low
            breakout_status = 'Up' if is_breaking_up else ('Down' if is_breaking_down else 'None')
            
            # 获取衍生品数据（带错误处理）
            oi_change = await get_oi_change(exchange, symbol, timeframe, lookback)
            funding_rate = await get_funding_rate(exchange, symbol)
            volume_24h = await get_24h_volume(exchange, symbol)
            
            # 核心指标计算
            momentum = calculate_momentum(df, lookback, idx)
            volume_ratio = calculate_volume_ratio(df, lookback, idx)
            rel_strength = calculate_relative_strength(df, btc_data_cache.get(timeframe), lookback, idx, momentum)
            
            timeframe_result = {
                'momentum': momentum,
                'rsi': rsi,
                'volume_ratio': volume_ratio,
                'rel_strength': rel_strength,
                'funding_rate': funding_rate,
                'oi_change': oi_change,
                'adx': adx,
                'bbw': bbw,
                'breakout_status': breakout_status,
                'volume_24h': volume_24h,
                'close': current_close,
                'ema_20': ema_20,
                'recent_high': recent_high,
                'recent_low': recent_low,
            }
            all_timeframe_results.append(timeframe_result)
            
        except Exception as e:
            logger.warning(f"Error processing {symbol} on {timeframe}: {e}")
            continue
    
    if not all_timeframe_results:
        return None
    
    # 多时间周期加权平均
    return calculate_weighted_average(all_timeframe_results, CONFIG.timeframe_weights, symbol)

async def get_oi_change(exchange, symbol: str, timeframe: str, lookback: int) -> float:
    """获取持仓量变化"""
    try:
        oi_history = await retry_async(
            exchange.fetch_open_interest_history,
            symbol, timeframe, 
            limit=lookback,
            max_retries=2
        )
        
        if not oi_history or len(oi_history) < 2:
            return 0.0
            
        oi_df = pd.DataFrame(oi_history)
        oi_df['openInterest'] = pd.to_numeric(oi_df['openInterestValue'])
        
        if oi_df['openInterest'].iloc[0] > 0:
            return (oi_df['openInterest'].iloc[-1] / oi_df['openInterest'].iloc[0] - 1) * 100
            
    except Exception as e:
        logger.debug(f"Failed to get OI change for {symbol}: {e}")
    
    return 0.0

async def get_funding_rate(exchange, symbol: str) -> float:
    """获取资金费率"""
    try:
        funding_data = await retry_async(exchange.fetch_funding_rate, symbol, max_retries=2)
        return funding_data.get('fundingRate', 0.0)
    except Exception as e:
        logger.debug(f"Failed to get funding rate for {symbol}: {e}")
        return 0.0

async def get_24h_volume(exchange, symbol: str) -> float:
    """获取24小时成交量"""
    try:
        ticker_data = await retry_async(exchange.fetch_ticker, symbol, max_retries=2)
        return ticker_data.get('quoteVolume', 0.0)
    except Exception as e:
        logger.debug(f"Failed to get 24h volume for {symbol}: {e}")
        return 0.0

def calculate_momentum(df: pd.DataFrame, lookback: int, idx: int) -> float:
    """计算动量"""
    try:
        current_price = df['close'].iloc[idx]
        past_price = df['close'].iloc[idx - lookback]
        if past_price > 0:
            return (current_price / past_price - 1) * 100
    except (IndexError, ZeroDivisionError):
        pass
    return 0.0

def calculate_volume_ratio(df: pd.DataFrame, lookback: int, idx: int) -> float:
    """计算成交量比率"""
    try:
        avg_vol_short = df['volume'].iloc[idx-5:idx].mean()
        avg_vol_long = df['volume'].iloc[idx-lookback:idx].mean()
        if avg_vol_long > 0:
            return avg_vol_short / avg_vol_long
    except (IndexError, ZeroDivisionError):
        pass
    return 1.0

def calculate_relative_strength(df: pd.DataFrame, btc_df: Optional[pd.DataFrame], 
                              lookback: int, idx: int, momentum: float) -> float:
    """计算相对强度"""
    if btc_df is None or len(btc_df) < lookback + 27:
        return momentum
    
    try:
        btc_momentum = calculate_momentum(btc_df, lookback, idx)
        return momentum - btc_momentum
    except Exception:
        return momentum

def calculate_weighted_average(results: List[Dict], weights: List[float], symbol: str) -> Dict:
    """计算多时间周期加权平均"""
    final_result = {'symbol': symbol}
    
    # 确保权重长度匹配
    current_weights = weights[:len(results)]
    if sum(current_weights) == 0:
        current_weights = [1.0 / len(results)] * len(results)
    else:
        # 归一化权重
        weight_sum = sum(current_weights)
        current_weights = [w / weight_sum for w in current_weights]
    
    for key in results[0].keys():
        if key in ['breakout_status', 'volume_24h']:
            final_result[key] = results[0][key]
            continue
            
        values = [res[key] for res in results if pd.notna(res[key])]
        if values:
            final_result[key] = np.average(values, weights=current_weights[:len(values)])
        else:
            final_result[key] = 0.0
    
    return final_result

async def full_market_scan(exchange) -> Tuple[pd.DataFrame, Optional[pd.DataFrame]]:
    """全市场扫描（优化版）"""
    # 获取所有永续合约品种
    all_symbols = await fetch_perpetual_symbols(exchange)
    if not all_symbols:
        return pd.DataFrame(), None
    
    # 获取成交量最大的品种
    symbols = await get_top_volume_symbols(exchange, all_symbols, CONFIG.top_n)
    if not symbols:
        return pd.DataFrame(), None
    
    # 获取BTC数据缓存
    btc_data_cache = await get_btc_data_cache(exchange)
    
    # 并发计算所有品种的强度因子
    console.log(f"Calculating strength factors for {len(symbols)} symbols...")
    results = await calculate_all_strength_factors(exchange, symbols, btc_data_cache)
    
    if not results:
        console.log("[bold red]No valid results from strength factor calculation.[/bold red]")
        return pd.DataFrame(), None
    
    # 创建DataFrame并进行后处理
    df = pd.DataFrame(results).set_index('symbol')
    df = process_strength_scores(df)
    
    # 计算相关性矩阵（带缓存）
    correlation_matrix = await get_correlation_matrix(exchange, df.index.tolist())
    
    return df.reset_index(), correlation_matrix

async def get_btc_data_cache(exchange) -> Dict:
    """获取BTC数据缓存"""
    cache_key = "btc_data_cache"
    cached_data = cache.get(cache_key, max_age_minutes=CONFIG.market_data_cache_minutes)
    
    if cached_data:
        logger.info("Using cached BTC data")
        return cached_data
    
    console.log("Fetching BTC data for all timeframes...")
    btc_data_cache = {}
    
    try:
        btc_tasks = [
            retry_async(exchange.fetch_ohlcv, 'BTC/USDT', tf, limit=CONFIG.lookback + 100)
            for tf in CONFIG.timeframes
        ]
        all_btc_ohlcv = await asyncio.gather(*btc_tasks, return_exceptions=True)
        
        for i, tf in enumerate(CONFIG.timeframes):
            if not isinstance(all_btc_ohlcv[i], Exception):
                btc_df = pd.DataFrame(
                    all_btc_ohlcv[i], 
                    columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                )
                if len(btc_df) >= CONFIG.lookback + 27:
                    btc_data_cache[tf] = btc_df
        
        cache.set(cache_key, btc_data_cache)
        
    except Exception as e:
        logger.error(f"Failed to fetch BTC data: {e}")
    
    return btc_data_cache

async def calculate_all_strength_factors(exchange, symbols: List[str], btc_data_cache: Dict) -> List[Dict]:
    """并发计算所有品种的强度因子"""
    tasks = [
        calculate_strength_factors(
            exchange, symbol, 
            CONFIG.timeframes, 
            CONFIG.lookback, 
            btc_data_cache
        )
        for symbol in symbols
    ]
    
    results = []
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
    ) as progress:
        scan_task = progress.add_task("[cyan]Scanning market...", total=len(tasks))
        
        for future in asyncio.as_completed(tasks):
            try:
                result = await future
                if result:
                    results.append(result)
            except Exception as e:
                logger.warning(f"Error processing a symbol: {e}")
            finally:
                progress.update(scan_task, advance=1)
    
    return results

def process_strength_scores(df: pd.DataFrame) -> pd.DataFrame:
    """处理强度分数（包括异常值处理和Z-Score计算）"""
    if df.empty:
        return df
    
    # 异常值处理（Winsorization）
    factors_to_winsorize = ['momentum', 'rsi', 'volume_ratio', 'rel_strength', 'funding_rate', 'oi_change', 'volume_24h']
    for factor in factors_to_winsorize:
        if factor in df.columns and len(df[factor].dropna()) > 5:  # 确保有足够的数据点
            p_low = df[factor].quantile(0.025)
            p_high = df[factor].quantile(0.975)
            df[factor] = np.clip(df[factor], p_low, p_high)
    
    # Z-Score归一化
    for factor_with_z, weight in FACTOR_WEIGHTS.items():
        base_factor = factor_with_z.replace('_z', '')
        if base_factor in df.columns:
            factor_data = df[base_factor].dropna()
            if len(factor_data) > 1 and factor_data.std() > 0:  # 确保有变异性
                df[factor_with_z] = (df[base_factor] - factor_data.mean()) / factor_data.std()
            else:
                df[factor_with_z] = 0.0
    
    # 计算最终强度分
    df['strength'] = 0
    for factor_z, weight in FACTOR_WEIGHTS.items():
        if factor_z in df.columns:
            df['strength'] += weight * df[factor_z].fillna(0)
    
    return df.sort_values('strength', ascending=False).dropna(subset=['strength'])

async def get_correlation_matrix(exchange, symbols: List[str]) -> Optional[pd.DataFrame]:
    """获取相关性矩阵（带缓存）"""
    cache_key = f"correlation_matrix_{len(symbols)}"
    cached_matrix = cache.get(cache_key, max_age_minutes=CONFIG.correlation_cache_hours * 60)
    
    if cached_matrix is not None:
        logger.info("Using cached correlation matrix")
        return cached_matrix
    
    try:
        console.log("Calculating correlation matrix...")
        
        # 确保BTC在列表中
        if 'BTC/USDT' not in symbols:
            symbols = ['BTC/USDT'] + symbols
        
        # 并发获取历史数据
        corr_timeframe = '1d'
        corr_lookback = 90
        
        ohlcv_tasks = [
            retry_async(exchange.fetch_ohlcv, sym, corr_timeframe, limit=corr_lookback, max_retries=2)
            for sym in symbols
        ]
        results = await asyncio.gather(*ohlcv_tasks, return_exceptions=True)
        
        # 处理数据
        all_close_series = {}
        for sym, ohlcv_result in zip(symbols, results):
            if isinstance(ohlcv_result, list) and len(ohlcv_result) > 30:  # 至少30个数据点
                temp_df = pd.DataFrame(
                    ohlcv_result, 
                    columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
                )
                temp_df['timestamp'] = pd.to_datetime(temp_df['timestamp'], unit='ms')
                temp_df.set_index('timestamp', inplace=True)
                all_close_series[sym] = temp_df['close']
        
        if len(all_close_series) >= 2:
            close_prices_df = pd.concat(all_close_series, axis=1)
            close_prices_df.ffill(inplace=True)
            returns_df = close_prices_df.pct_change().dropna(how='all')
            returns_df.dropna(axis=1, how='all', inplace=True)
            
            if returns_df.shape[1] >= 2:
                correlation_matrix = returns_df.corr()
                cache.set(cache_key, correlation_matrix)
                return correlation_matrix
        
        logger.warning("Not enough data to build correlation matrix")
        return None
        
    except Exception as e:
        logger.error(f"Correlation calculation failed: {e}")
        return None

# 显示函数（保持原有逻辑，但增加错误处理）
def display_trend_pool(df: pd.DataFrame):
    """显示趋势策略池"""
    if df.empty:
        return
        
    table = Table(title="🏆 趋势策略池 (Trend Pool)", style="default", title_style="bold blue")
    table.add_column("Direction", justify="center")
    table.add_column("Symbol", justify="left", style="cyan")
    table.add_column("Strength", justify="right")
    table.add_column("ADX", justify="right")
    table.add_column("Notes", justify="left")

    # 使用配置的阈值
    longs = df[
        (df['strength'] > CONFIG.trend_strength_threshold) & 
        (df['adx'] > CONFIG.trend_adx_threshold)
    ].sort_values('strength', ascending=False)
    
    shorts = df[
        (df['strength'] < -CONFIG.trend_strength_threshold) & 
        (df['adx'] > CONFIG.trend_adx_threshold)
    ].sort_values('strength', ascending=True)

    for _, row in longs.iterrows():
        notes = []
        try:
            if abs(row['close'] - row['ema_20']) / row['close'] < 0.02:
                notes.append("Pullback")
            if row['volume_ratio'] > 1.5:
                notes.append("Volume Up")
        except (KeyError, ZeroDivisionError):
            pass
        
        table.add_row(
            "[bold green]LONG[/bold green]", 
            row['symbol'], 
            f"{row['strength']:.2f}", 
            f"{row['adx']:.2f}", 
            ", ".join(notes)
        )

    if not longs.empty and not shorts.empty:
        table.add_row("---", "---", "---", "---", "---")

    for _, row in shorts.iterrows():
        table.add_row(
            "[bold red]SHORT[/bold red]", 
            row['symbol'], 
            f"{row['strength']:.2f}", 
            f"{row['adx']:.2f}", 
            ""
        )

    if not longs.empty or not shorts.empty:
        console.print(table)
    else:
        console.print("[yellow]No trend opportunities found with current thresholds[/yellow]")

def display_range_pool(df: pd.DataFrame):
    """显示震荡/网格策略池"""
    if df.empty:
        return
        
    try:
        bbw_p20 = df['bbw'].quantile(0.20)
        bbw_p50 = df['bbw'].quantile(0.50)
        
        range_candidates = df[
            (df['strength'].between(*CONFIG.range_strength_range)) &
            (df['adx'] < CONFIG.range_adx_threshold) &
            (df['bbw'].between(bbw_p20, bbw_p50))
        ].sort_values('bbw', ascending=True)

        if range_candidates.empty:
            console.print("[yellow]No range/grid opportunities found[/yellow]")
            return

        table = Table(title="🔀 震荡/网格策略池 (Range/Grid Pool)", style="default", title_style="bold purple")
        table.add_column("Symbol", justify="left", style="cyan")
        table.add_column("Strength", justify="right")
        table.add_column("BBW", justify="right", style="yellow")
        table.add_column("Suggested Range", justify="center")

        for _, row in range_candidates.head(10).iterrows():
            table.add_row(
                row['symbol'],
                f"{row['strength']:.2f}",
                f"{row['bbw']:.4f}",
                f"{row['recent_low']:.4f} - {row['recent_high']:.4f}"
            )
        console.print(table)
        
    except Exception as e:
        logger.error(f"Error displaying range pool: {e}")

def display_pairs_pool(df: pd.DataFrame, correlation_matrix: Optional[pd.DataFrame]):
    """显示配对交易策略池"""
    if correlation_matrix is None or df.empty:
        console.print("[yellow]No correlation data available for pairs trading[/yellow]")
        return

    try:
        long_candidates = df.sort_values('strength', ascending=False).head(5)
        short_candidates = df.sort_values('strength', ascending=True).head(5)

        if long_candidates.empty or short_candidates.empty:
            return

        pairs = []
        for _, long_row in long_candidates.iterrows():
            for _, short_row in short_candidates.iterrows():
                long_sym = long_row['symbol']
                short_sym = short_row['symbol']
                
                if long_sym == short_sym:
                    continue

                if long_sym in correlation_matrix.index and short_sym in correlation_matrix.columns:
                    corr = correlation_matrix.loc[long_sym, short_sym]
                    if corr < CONFIG.correlation_threshold:
                        strength_diff = long_row['strength'] - short_row['strength']
                        pairs.append((
                            long_sym, long_row['strength'], 
                            short_sym, short_row['strength'], 
                            strength_diff, corr
                        ))

        if not pairs:
            console.print("[yellow]No suitable pairs found with current correlation threshold[/yellow]")
            return

        pairs.sort(key=lambda x: x[4], reverse=True)

        table = Table(title="⚖️ 配对交易策略池 (Pairs Trading Pool)", style="default", title_style="bold yellow")
        table.add_column("Long Candidate", justify="left")
        table.add_column("Short Candidate", justify="left")
        table.add_column("Strength Diff.", justify="right")
        table.add_column("Correlation", justify="right")

        for long_s, long_str, short_s, short_str, str_diff, corr_val in pairs[:10]:
            table.add_row(
                f"[green]{long_s}[/] ({long_str:.2f})",
                f"[red]{short_s}[/] ({short_str:.2f})",
                f"[bold]{str_diff:.2f}[/bold]",
                f"{corr_val:.3f}"
            )
        console.print(table)
        
    except Exception as e:
        logger.error(f"Error displaying pairs pool: {e}")

def display_strategy_pools(full_df: pd.DataFrame, correlation_matrix: Optional[pd.DataFrame]):
    """显示所有策略池"""
    console.print("\n[bold]===== 市场机会扫描仪 (优化版) =====[/bold]")
    display_trend_pool(full_df)
    display_range_pool(full_df)
    display_pairs_pool(full_df, correlation_matrix)

async def send_feishu_notification_async(full_df: pd.DataFrame, correlation_matrix: Optional[pd.DataFrame]):
    """异步发送飞书通知"""
    webhook_url = CONFIG.feishu_webhook_url
    if not webhook_url or 'YOUR_WEBHOOK_KEY' in webhook_url:
        logger.info("Feishu Webhook URL not configured, skipping notification.")
        return

    try:
        # 构建消息内容（简化版，避免过于复杂）
        message_content = build_feishu_message(full_df, correlation_matrix)
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                webhook_url,
                json=message_content,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("StatusCode") == 0:
                        console.log("Feishu notification sent successfully.")
                    else:
                        logger.error(f"Feishu API error: {result.get('StatusMessage')}")
                else:
                    logger.error(f"HTTP error {response.status} when sending Feishu notification")
                    
    except Exception as e:
        logger.error(f"Error sending Feishu notification: {e}")

def build_feishu_message(full_df: pd.DataFrame, correlation_matrix: Optional[pd.DataFrame]) -> dict:
    """构建飞书消息内容"""
    # 简化的消息构建逻辑
    elements = []
    
    # 趋势池摘要
    longs = full_df[
        (full_df['strength'] > CONFIG.trend_strength_threshold) & 
        (full_df['adx'] > CONFIG.trend_adx_threshold)
    ]
    shorts = full_df[
        (full_df['strength'] < -CONFIG.trend_strength_threshold) & 
        (full_df['adx'] > CONFIG.trend_adx_threshold)
    ]
    
    trend_summary = f"趋势机会: {len(longs)}个多头, {len(shorts)}个空头"
    elements.append({"tag": "div", "text": {"content": trend_summary, "tag": "lark_md"}})
    
    # 构建完整的卡片消息
    return {
        "msg_type": "interactive",
        "card": {
            "config": {"wide_screen_mode": True},
            "header": {
                "title": {
                    "content": f"📈 市场扫描仪 - {datetime.now().strftime('%H:%M:%S')}", 
                    "tag": "plain_text"
                },
                "template": "blue"
            },
            "elements": elements
        }
    }

async def run_realtime_monitor():
    """主监控循环（优化版）"""
    exchange = ccxt.pro.binance({
        'enableRateLimit': True,
        'options': {'defaultType': 'future'},
        'timeout': 30000,  # 30秒超时
    })
    
    try:
        scan_count = 0
        while True:
            scan_count += 1
            console.rule(f"[bold yellow]Scan #{scan_count} at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            try:
                full_df, correlation_matrix = await full_market_scan(exchange)
                
                if not full_df.empty:
                    display_strategy_pools(full_df, correlation_matrix)
                    
                    # 发送飞书通知
                    await send_feishu_notification_async(full_df, correlation_matrix)
                    
                    # 保存结果
                    filename = f"market_strength_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
                    full_df.to_csv(filename, index=False)
                    console.log(f"Results saved to [underline]{filename}[/underline]")
                    
                    # 显示缓存状态
                    console.log(f"Cache entries: {len(cache.cache)}")
                else:
                    console.log("[bold red]Scan returned no data[/bold red]")
                
            except Exception as e:
                logger.error(f"Error during scan #{scan_count}: {e}")
                console.log(f"[bold red]Scan #{scan_count} failed: {e}[/bold red]")
            
            console.log(f"Waiting {CONFIG.scan_interval_seconds} seconds for next scan...")
            await asyncio.sleep(CONFIG.scan_interval_seconds)
            
    except KeyboardInterrupt:
        console.log("Monitor stopped by user.")
    except Exception as e:
        logger.error(f"Fatal error in monitor: {e}")
    finally:
        try:
            await exchange.close()
        except Exception as e:
            logger.error(f"Error closing exchange: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(run_realtime_monitor())
    except KeyboardInterrupt:
        console.log("Program terminated by user.")
    except Exception as e:
        logger.error(f"Program crashed: {e}")
        console.log(f"[bold red]Program crashed: {e}[/bold red]")