#!/usr/bin/env python3
"""
扫描器测试脚本
用于验证基本功能是否正常
"""

import asyncio
import sys
from rich.console import Console
from scanner_config import get_config
import scanner_optimized

console = Console()

async def test_basic_functions():
    """测试基本功能"""
    console.print("[bold blue]开始测试扫描器基本功能...[/bold blue]")
    
    # 使用测试配置（减少处理数量以加快测试）
    test_config = get_config('default')
    test_config.top_n = 10  # 只处理10个品种
    test_config.scan_interval_seconds = 60  # 1分钟间隔
    
    # 应用配置
    scanner_optimized.CONFIG = test_config
    scanner_optimized.FACTOR_WEIGHTS = test_config.factor_weights
    
    try:
        # 创建交易所实例
        exchange = scanner_optimized.ccxt.pro.binance({
            'enableRateLimit': True,
            'options': {'defaultType': 'future'},
            'timeout': 30000,
        })
        
        console.print("✓ 交易所连接创建成功")
        
        # 测试获取永续合约品种
        console.print("测试获取永续合约品种...")
        symbols = await scanner_optimized.fetch_perpetual_symbols(exchange)
        if symbols:
            console.print(f"✓ 成功获取 {len(symbols)} 个永续合约品种")
        else:
            console.print("✗ 获取永续合约品种失败")
            return False
        
        # 测试获取成交量最大的品种
        console.print("测试获取成交量排名...")
        top_symbols = await scanner_optimized.get_top_volume_symbols(exchange, symbols, 5)
        if top_symbols:
            console.print(f"✓ 成功获取前5个高成交量品种: {', '.join(top_symbols[:3])}...")
        else:
            console.print("✗ 获取成交量排名失败")
            return False
        
        # 测试BTC数据获取
        console.print("测试BTC数据获取...")
        btc_cache = await scanner_optimized.get_btc_data_cache(exchange)
        if btc_cache:
            console.print(f"✓ 成功获取BTC数据，时间周期: {list(btc_cache.keys())}")
        else:
            console.print("⚠ BTC数据获取失败，但不影响基本功能")
        
        # 测试单个品种的强度因子计算
        console.print("测试强度因子计算...")
        test_symbol = top_symbols[0] if top_symbols else 'BTC/USDT'
        result = await scanner_optimized.calculate_strength_factors(
            exchange, test_symbol, 
            test_config.timeframes, 
            test_config.lookback, 
            btc_cache
        )
        
        if result:
            console.print(f"✓ 成功计算 {test_symbol} 的强度因子")
            console.print(f"  - 强度分: {result.get('strength', 'N/A')}")
            console.print(f"  - 动量: {result.get('momentum', 'N/A'):.2f}%")
            console.print(f"  - RSI: {result.get('rsi', 'N/A'):.2f}")
        else:
            console.print(f"✗ {test_symbol} 强度因子计算失败")
            return False
        
        # 关闭交易所连接
        await exchange.close()
        console.print("✓ 交易所连接已关闭")
        
        console.print("\n[bold green]所有基本功能测试通过！[/bold green]")
        console.print("[yellow]现在可以运行完整的扫描器了[/yellow]")
        return True
        
    except Exception as e:
        console.print(f"[bold red]测试过程中出现错误: {e}[/bold red]")
        return False

async def test_quick_scan():
    """快速扫描测试"""
    console.print("\n[bold blue]开始快速扫描测试...[/bold blue]")
    
    # 使用更小的配置进行快速测试
    test_config = get_config('default')
    test_config.top_n = 5
    
    scanner_optimized.CONFIG = test_config
    scanner_optimized.FACTOR_WEIGHTS = test_config.factor_weights
    
    try:
        exchange = scanner_optimized.ccxt.pro.binance({
            'enableRateLimit': True,
            'options': {'defaultType': 'future'},
            'timeout': 30000,
        })
        
        # 执行一次完整扫描
        console.print("执行快速市场扫描...")
        df, correlation_matrix = await scanner_optimized.full_market_scan(exchange)
        
        if not df.empty:
            console.print(f"✓ 扫描成功，处理了 {len(df)} 个品种")
            
            # 显示结果
            scanner_optimized.display_strategy_pools(df, correlation_matrix)
            
            console.print("\n[bold green]快速扫描测试成功！[/bold green]")
        else:
            console.print("✗ 扫描返回空结果")
            return False
        
        await exchange.close()
        return True
        
    except Exception as e:
        console.print(f"[bold red]快速扫描测试失败: {e}[/bold red]")
        return False

def main():
    """主测试函数"""
    console.print("[bold]扫描器功能测试[/bold]")
    console.print("这将测试扫描器的基本功能，不会进行长时间运行")
    
    try:
        # 基本功能测试
        basic_test_result = asyncio.run(test_basic_functions())
        
        if basic_test_result:
            # 询问是否进行快速扫描测试
            console.print("\n基本功能测试通过！")
            response = input("是否进行快速扫描测试？(y/n): ").lower().strip()
            
            if response in ['y', 'yes', '是']:
                quick_scan_result = asyncio.run(test_quick_scan())
                if quick_scan_result:
                    console.print("\n[bold green]所有测试通过！扫描器已准备就绪。[/bold green]")
                    console.print("\n使用以下命令启动扫描器:")
                    console.print("  python run_scanner.py")
                    console.print("  python run_scanner.py --config conservative")
                    console.print("  python run_scanner.py --config aggressive")
                else:
                    console.print("\n[bold yellow]快速扫描测试失败，但基本功能正常[/bold yellow]")
            else:
                console.print("\n[bold green]基本功能测试完成！[/bold green]")
        else:
            console.print("\n[bold red]基本功能测试失败，请检查网络连接和依赖[/bold red]")
            sys.exit(1)
            
    except KeyboardInterrupt:
        console.print("\n[yellow]测试被用户中断[/yellow]")
    except Exception as e:
        console.print(f"\n[bold red]测试过程中出现未预期的错误: {e}[/bold red]")
        sys.exit(1)

if __name__ == "__main__":
    main()