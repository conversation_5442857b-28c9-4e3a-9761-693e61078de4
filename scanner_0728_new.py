# ==============================================================================
# === 加密货币市场机会扫描器 V4 - 单文件最终版 ===
# ==============================================================================
# 作者: [您的名字]
# 版本: 4.0.0
# 描述: 一个全天候、双引擎的市场扫描仪，结合了宏观判断、动量趋势跟踪和
#       极端值反转捕捉，并将结果统一输出到终端和飞书。
# ==============================================================================

# --- 核心依赖 ---
import sys
import asyncio
import json
import time
from datetime import datetime

# --- 第三方库 ---
import numpy as np
import pandas as pd
import pandas_ta as ta
import ccxt.pro
import requests
from scipy.signal import find_peaks

# --- Rich 用于美化终端输出 ---
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn
from rich.rule import Rule

# ==============================================================================
# === 0. 初始化设置与兼容性修复 ===
# ==============================================================================

# 修复 pandas_ta 与新版 NumPy 的兼容性问题
if not hasattr(np, 'NaN'):
    np.NaN = np.nan

# 修复在 Windows 上的 aiodns/asyncio 兼容性问题
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# ==============================================================================
# === 1. 统一配置中心 ===
# ==============================================================================
CONFIG = {
    # --- 全局设置 ---
    'scan_interval_seconds': 3600,  # 扫描间隔时间（秒），3600秒 = 1小时
    'top_n_by_volume': 50,          # 按24小时交易额筛选前50个币种进行动量扫描
    'feishu_webhook_url': 'https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_WEBHOOK_URL_HERE', # !!! 请替换为你自己的飞书机器人Webhook URL !!!

    # --- 市场状态引擎配置 ---
    'market_status': {
        'base_symbols': ['BTC/USDT', 'ETH/USDT'], # 用于判断大盘方向的核心资产
        'daily_ema_periods': [50, 200],          # 判断日线趋势的关键EMA周期
        'breadth_timeframe': '4h',               # 计算市场广度的K线周期
        'breadth_ema': 20,                       # 计算市场广度所用的EMA周期
    },

    # --- 动量引擎配置 ---
    'momentum_engine': {
        'timeframes': ['4h', '1d'],              # 多时间周期分析
        'timeframe_weights': [0.6, 0.4],         # 各时间周期的权重
        'lookback': 20,                          # 动量和指标的回看周期
        'factor_weights': {                      # V4版因子权重，资金费率已移出
            'rel_strength_z': 0.6,               # 相对强度 (Alpha) 的权重
            'volume_24h_z': 0.4,                 # 流动性 (成交额) 的权重
        },
        'correlation_timeframe': '1d',           # 计算相关性的K线周期
        'correlation_lookback': 90,              # 计算相关性的回看天数
    },

    # --- 极端值引擎配置 ---
    'extremity_engine': {
        'enabled': True,                         # 是否启用此引擎
        'scan_symbols': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT', 'XRP/USDT', 'LDO/USDT', 'LINK/USDT'], # 指定扫描高市值币种
        'calibration_lookback_days': 365,        # 校准时回看的天数，用于计算历史分位数
        'timeframe': '1d',                       # 极端值分析的K线周期
        'percentiles': [0.01, 0.05, 0.10, 0.90, 0.95, 0.99], # 要计算的历史分位数
        'factors': {                             # 用于寻找反转机会的因子
            'RSI_14': {'type': 'oscillator_low'},          # RSI，越低分越高
            'MFI_14': {'type': 'oscillator_low'},          # MFI，资金流指标，越低分越高
            'BBP_20_2.0': {'type': 'oscillator_low'},       # 布林带百分比B，越低分越高 (超卖)
            'price_vs_ema200_pct': {'type': 'deviation_low'}, # 价格与EMA200的偏离度，负偏离越大分越高
            'BBW_20_2.0': {'type': 'volatility_squeeze'},  # 布林带宽度，越窄（历史低位）分越高，预示着潜在的大波动
        },
        'score_map': {                           # 分位数对应的分数
            'low': {0.01: 40, 0.05: 20, 0.10: 10},  # 低于x分位数得分
            'high': {0.99: 40, 0.95: 20, 0.90: 10} # 高于y分位数得分
        },
        'divergence_bonus': 30,                  # 发现看涨背离时的额外加分
        'alert_threshold_score': 50,             # 触发警报的最低总分
    }
}

# --- 全局变量 ---
calibration_cache = {}  # 用于存储校准数据的全局缓存
console = Console()     # Rich终端输出实例

# ==============================================================================
# === 2. 辅助工具函数 ===
# ==============================================================================
def find_bullish_divergence(price_series, indicator_series, lookback=30):
    """检测看涨背离（价格创更低低点，指标未创新低）"""
    try:
        price, indicator = price_series.tail(lookback), indicator_series.tail(lookback)
        # 使用scipy.signal.find_peaks寻找局部低点（通过取负数）
        price_lows_indices, _ = find_peaks(-price, distance=5)
        if len(price_lows_indices) < 2: return False
        
        # 获取最近的两个低点的时间戳索引
        last_low_idx = price.index[price_lows_indices[-1]]
        prev_low_idx = price.index[price_lows_indices[-2]]
        
        # 检查价格是否创下更低的低点 (LL) 且指标创下更高的低点 (HL)
        if price.loc[last_low_idx] < price.loc[prev_low_idx] and indicator.loc[last_low_idx] > indicator.loc[prev_low_idx]:
            return True
    except Exception:
        return False
    return False

# ==============================================================================
# === 3. 核心分析引擎 ===
# ==============================================================================

# --- 3.1 市场状态引擎 ---
async def get_market_status(exchange, top_symbols):
    """计算宏观市场状态，判断当前是牛市、熊市还是震荡市"""
    cfg = CONFIG['market_status']
    status_score = 0
    reasons = []

    # 1. 分析核心资产（BTC/ETH）的日线趋势
    for symbol in cfg['base_symbols']:
        try:
            ohlcv = await exchange.fetch_ohlcv(symbol, '1d', limit=max(cfg['daily_ema_periods']) + 5)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            if len(df) < max(cfg['daily_ema_periods']): continue
            
            close = df['close'].iloc[-1]
            for ema_period in cfg['daily_ema_periods']:
                ema_val = ta.ema(df['close'], length=ema_period).iloc[-1]
                if close > ema_val:
                    score_add = 15 if ema_period == 50 else 25 # 站上EMA200权重更高
                    status_score += score_add
                    reasons.append(f"{symbol.split('/')[0]}>D_EMA{ema_period}")
        except Exception:
            continue

    # 2. 计算市场广度（有多少主流币处于短期均线之上）
    try:
        tasks = [exchange.fetch_ohlcv(s, cfg['breadth_timeframe'], limit=cfg['breadth_ema'] + 2) for s in top_symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        above_ema_count = 0
        valid_symbols = 0
        for res in results:
            if isinstance(res, list) and len(res) >= cfg['breadth_ema'] + 1:
                valid_symbols += 1
                df = pd.DataFrame(res, columns=['timestamp','o','h','l','close','v'])
                ema_val = ta.ema(df['close'], length=cfg['breadth_ema']).iloc[-1]
                if df['close'].iloc[-1] > ema_val:
                    above_ema_count += 1
        
        if valid_symbols > 0:
            breadth_pct = (above_ema_count / valid_symbols) * 100
            reasons.append(f"广度:{breadth_pct:.0f}%")
            if breadth_pct > 60: status_score += 20  # 市场强势
            elif breadth_pct < 40: status_score -= 20 # 市场弱势
    except Exception:
        pass

    # 3. 根据总分判断最终市场状态
    if status_score >= 80: status = "🔥 极度看涨"
    elif status_score >= 40: status = "🟢 看涨"
    elif status_score <= -80: status = "🧊 极度看跌"
    elif status_score <= -40: status = "🔴 看跌"
    else: status = "⚪ 中性"
        
    return {'status': status, 'score': status_score, 'details': ", ".join(reasons)}


# --- 3.2 极端值引擎 ---
async def calibrate_extremity_engine(exchange):
    """在程序启动时运行一次，计算各指标在过去一年的历史分位数，作为动态阈值。"""
    global calibration_cache
    cfg = CONFIG['extremity_engine']
    if not cfg['enabled']: return
    console.print(Rule("[bold blue]启动极端值引擎校准程序[/bold blue]"))
    
    for symbol in cfg['scan_symbols']:
        try:
            ohlcv = await exchange.fetch_ohlcv(symbol, cfg['timeframe'], limit=cfg['calibration_lookback_days'])
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            # 一次性计算所有需要校准的因子
            df.ta.rsi(length=14, append=True)
            df.ta.mfi(length=14, append=True)
            df.ta.bbands(length=20, append=True)
            if 'BBP_20_2.0' not in df.columns: # 兼容旧版pandas_ta
                df['BBP_20_2.0'] = (df['close'] - df['BBL_20_2.0']) / (df['BBU_20_2.0'] - df['BBL_20_2.0'])
            ema200 = ta.ema(df['close'], length=200)
            df['price_vs_ema200_pct'] = (df['close'] / ema200 - 1) * 100

            symbol_thresholds = {}
            for factor_name in cfg['factors'].keys():
                if factor_name in df.columns:
                    # 计算并存储该因子的所有百分位数值
                    symbol_thresholds[factor_name] = df[factor_name].quantile(cfg['percentiles']).to_dict()
            calibration_cache[symbol] = symbol_thresholds
            console.log(f"  ✅ [cyan]{symbol}[/cyan] 的动态阈值校准成功。")
        except Exception as e:
            console.log(f"  ❌ [cyan]{symbol}[/cyan] 的动态阈值校准失败: {e}")
    console.print(Rule("[bold green]校准程序完成[/bold green]"))


async def run_extremity_scan(exchange):
    """扫描指定币种，寻找指标达到历史极值的反转机会。"""
    cfg = CONFIG['extremity_engine']
    if not cfg['enabled'] or not calibration_cache: return pd.DataFrame()

    results = []
    for symbol in cfg['scan_symbols']:
        try:
            # 获取用于分析的近期数据
            ohlcv = await exchange.fetch_ohlcv(symbol, cfg['timeframe'], limit=250)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            if len(df) < 200 or symbol not in calibration_cache: continue

            # 计算当前最新的指标值
            df.ta.rsi(length=14, append=True)
            df.ta.mfi(length=14, append=True)
            df.ta.bbands(length=20, append=True)
            if 'BBP_20_2.0' not in df.columns:
                 df['BBP_20_2.0'] = (df['close'] - df['BBL_20_2.0']) / (df['BBU_20_2.0'] - df['BBL_20_2.0'])
            ema200 = ta.ema(df['close'], length=200)
            df['price_vs_ema200_pct'] = (df['close'] / ema200 - 1) * 100

            total_score, reasons, details = 0, [], {}
            thresholds = calibration_cache[symbol]

            # 将当前值与校准好的历史分位数进行比较，并计分
            for factor, f_cfg in cfg['factors'].items():
                current_value = df[factor].iloc[-1] if factor in df.columns else np.nan
                if pd.isna(current_value): continue
                details[factor] = f"{current_value:.2f}"
                if factor not in thresholds: continue

                score_type = f_cfg['type']
                score_map = cfg['score_map']
                # 判断是应该找低点还是高点
                if score_type in ['oscillator_low', 'deviation_low', 'volatility_squeeze']:
                    for p, score in score_map['low'].items():
                        if current_value < thresholds[factor][p]:
                            total_score += score
                            reasons.append(f"{factor.split('_')[0]}<{p*100}%({score})")
                            break # 只取最高分
            
            # 检查是否存在看涨背离，如果存在则加分
            if find_bullish_divergence(df['close'], df['RSI_14']):
                total_score += cfg['divergence_bonus']
                reasons.append(f"RSI看涨背离({cfg['divergence_bonus']})")
                details['Divergence'] = "RSI Bullish"

            # 如果总分超过阈值，则记录该机会
            if total_score >= cfg['alert_threshold_score']:
                results.append({'symbol': symbol, 'total_score': total_score, 'details': details, 'reasons': ", ".join(reasons)})
        except Exception:
            continue

    if not results: return pd.DataFrame()
    return pd.DataFrame(results).sort_values('total_score', ascending=False)

# --- 3.3 动量引擎 ---
async def calculate_momentum_factors(exchange, symbol, btc_data_cache, cfg):
    """为单个币种计算多时间周期的动量相关因子。"""
    all_timeframe_results = []
    for timeframe in cfg['timeframes']:
        try:
            # 获取K线数据，多获取一些以保证指标计算准确
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=cfg['lookback'] + 100)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            if len(df) < cfg['lookback'] + 27: continue # 数据不足则跳过

            # 使用pandas_ta计算基础指标
            df.ta.rsi(length=14, append=True)
            df.ta.adx(length=cfg['lookback'], append=True)
            df.ta.bbands(length=cfg['lookback'], append=True)

            # 从已收盘的K线（倒数第二根）获取指标值，避免未来函数
            adx = df[f'ADX_{cfg["lookback"]}'].iloc[-2]
            bbw = df[f'BBW_{cfg["lookback"]}_2.0'].iloc[-2]
            rsi = df['RSI_14'].iloc[-2]
            
            # 核心动量因子：价格变化率 (ROC)
            momentum = (df['close'].iloc[-2] / df['close'].iloc[-cfg['lookback']-2] - 1) * 100
            
            # 相对强度 = 自身动量 - BTC动量
            rel_strength = momentum
            if btc_data_cache.get(timeframe) is not None:
                btc_df = btc_data_cache[timeframe]
                btc_momentum = (btc_df['close'].iloc[-2] / btc_df['close'].iloc[-cfg['lookback']-2] - 1) * 100
                rel_strength = momentum - btc_momentum
            
            # 获取衍生品数据作为风险/情绪指标
            oi_change = 0.0
            try:
                oi_history = await exchange.fetch_open_interest_history(symbol, timeframe, limit=cfg['lookback'])
                if oi_history and len(oi_history) > 1:
                    oi_df = pd.DataFrame(oi_history)
                    # 使用名义价值计算OI变化
                    oi_change = (float(oi_df['openInterestValue'].iloc[-1]) / float(oi_df['openInterestValue'].iloc[0]) - 1) * 100
            except Exception: pass

            funding_rate = 0.0
            try:
                funding_data = await exchange.fetch_funding_rate(symbol)
                funding_rate = funding_data.get('fundingRate', 0.0) * 100 # 转换为百分比
            except Exception: pass
            
            # 获取24小时成交额（以USDT计价），作为流动性指标
            volume_24h = 0.0
            try:
                ticker = await exchange.fetch_ticker(symbol)
                volume_24h = ticker.get('quoteVolume', 0.0)
            except Exception: pass

            all_timeframe_results.append({
                'rel_strength': rel_strength, 'volume_24h': volume_24h,
                'adx': adx, 'bbw': bbw, 'rsi': rsi,
                'funding_rate': funding_rate, 'oi_change': oi_change,
            })
        except Exception:
            continue
    
    if not all_timeframe_results: return None

    # 对多时间周期的结果进行加权平均
    final_result = {}
    weights = cfg['timeframe_weights']
    for key in all_timeframe_results[0].keys():
        values = [res.get(key, 0) for res in all_timeframe_results]
        if key == 'volume_24h': # 24h成交额是全局的，不需要加权
            final_result[key] = values[0]
            continue
        final_result[key] = np.average(values, weights=weights[:len(values)])
            
    final_result['symbol'] = symbol
    return final_result


async def run_momentum_scan(exchange):
    """执行全市场的动量扫描，并返回排序后的结果和相关性矩阵。"""
    cfg = CONFIG['momentum_engine']
    all_symbols = await fetch_perpetual_symbols(exchange)
    if not all_symbols: return pd.DataFrame(), None, []

    # 1. 获取所有ticker，按交易额筛选出top N
    try:
        all_tickers = await exchange.fetch_tickers()
        volume_df = pd.DataFrame([
            {'symbol': s, 'quoteVolume': t['quoteVolume']} 
            for s, t in all_tickers.items() if s in all_symbols and t.get('quoteVolume')
        ])
        top_symbols = volume_df.sort_values('quoteVolume', ascending=False).head(CONFIG['top_n_by_volume'])['symbol'].tolist()
        console.log(f"已筛选出交易额最高的 [bold green]{len(top_symbols)}[/bold green] 个交易对进行动量扫描。")
    except Exception as e:
        console.log(f"[red]筛选交易对失败: {e}，将使用默认列表。[/red]")
        top_symbols = [s for s in all_symbols if s.endswith('/USDT')][:CONFIG['top_n_by_volume']]

    # 2. 提前获取BTC数据作为基准
    btc_data_cache = {}
    try:
        btc_tasks = [exchange.fetch_ohlcv('BTC/USDT', tf, limit=cfg['lookback'] + 100) for tf in cfg['timeframes']]
        all_btc_ohlcv = await asyncio.gather(*btc_tasks)
        for i, tf in enumerate(cfg['timeframes']):
            btc_data_cache[tf] = pd.DataFrame(all_btc_ohlcv[i], columns=['timestamp', 'o','h','l','close','v'])
    except Exception as e:
        console.log(f"[yellow]获取BTC基准数据失败，相对强度计算将受影响: {e}[/yellow]")

    # 3. 并发计算所有币种的动量因子
    tasks = [calculate_momentum_factors(exchange, symbol, btc_data_cache, cfg) for symbol in top_symbols]
    results = []
    with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(), TextColumn("[progress.percentage]{task.percentage:>3.0f}%")) as progress:
        scan_task = progress.add_task("[cyan]正在扫描市场动量...", total=len(tasks))
        for future in asyncio.as_completed(tasks):
            result = await future
            if result: results.append(result)
            progress.update(scan_task, advance=1)

    if not results: return pd.DataFrame(), None, top_symbols
    df = pd.DataFrame(results).set_index('symbol')
    
    # 4. 数据标准化处理 (Winsorization + Z-Score)
    for factor in ['rel_strength', 'volume_24h']:
        # 缩尾处理，去除极端异常值
        p_low, p_high = df[factor].quantile(0.025), df[factor].quantile(0.975)
        df[factor] = np.clip(df[factor], p_low, p_high)
        # Z-Score标准化，使得不同量纲的因子可以公平比较
        df[f'{factor}_z'] = (df[factor] - df[factor].mean()) / df[factor].std()

    # 5. 计算最终强度分
    df['strength'] = sum(weight * df[factor_z] for factor_z, weight in cfg['factor_weights'].items() if factor_z in df.columns)
    df = df.sort_values('strength', ascending=False).reset_index().dropna(subset=['strength'])
    
    # 6. 计算相关性矩阵
    correlation_matrix = None
    try:
        corr_symbols = df['symbol'].tolist()
        if 'BTC/USDT' not in corr_symbols: corr_symbols.insert(0, 'BTC/USDT')
        
        corr_tasks = [exchange.fetch_ohlcv(s, cfg['correlation_timeframe'], limit=cfg['correlation_lookback']) for s in corr_symbols]
        ohlcv_results = await asyncio.gather(*corr_tasks, return_exceptions=True)
        
        close_series = {}
        for sym, res in zip(corr_symbols, ohlcv_results):
            if isinstance(res, list) and len(res) > 1:
                temp_df = pd.DataFrame(res, columns=['timestamp','o','h','l','close','v'])
                temp_df['timestamp'] = pd.to_datetime(temp_df['timestamp'], unit='ms')
                close_series[sym] = temp_df.set_index('timestamp')['close']
        
        if len(close_series) > 1:
            returns_df = pd.concat(close_series, axis=1).pct_change().dropna(how='all')
            if returns_df.shape[1] > 1:
                correlation_matrix = returns_df.corr()
    except Exception as e:
        console.log(f"[yellow]相关性矩阵计算失败: {e}[/yellow]")
        
    return df, correlation_matrix, top_symbols


# ==============================================================================
# === 4. 报告模块 (终端显示与飞书通知) ===
# ==============================================================================
def display_all_pools(market_state, momentum_df, correlation_matrix, extremity_df):
    """主显示函数，将所有分析结果美观地打印到终端。"""
    console.print(Rule(f"[bold]📊 市场扫描结果 | 当前状态: {market_state['status']} ({market_state['details']})[/bold]", style="blue"))

    # --- 1. 极端值/反转池 ---
    if not extremity_df.empty:
        table = Table(title="🚨 反转策略池 (Extremity Engine)", style="default", title_style="bold magenta")
        table.add_column("交易对", style="cyan")
        details_keys = list(extremity_df.iloc[0]['details'].keys()) if not extremity_df.empty else []
        for key in details_keys:
            table.add_column(key.split('_')[0], justify="right")
        table.add_column("总分", justify="center", style="bold yellow")
        table.add_column("得分原因", justify="left", max_width=50)
        for _, row in extremity_df.iterrows():
            row_data = [row['symbol']] + [row['details'].get(k, "N/A") for k in details_keys] + [f"{row['total_score']}", row['reasons']]
            table.add_row(*row_data)
        console.print(table)
    
    # --- 2. 动量/趋势池 ---
    if not momentum_df.empty:
        longs = momentum_df[momentum_df['strength'] > 1.0].head(5)
        shorts = momentum_df[momentum_df['strength'] < -1.0].tail(5).sort_values('strength')
        if not longs.empty or not shorts.empty:
            table = Table(title="🚀 趋势策略池 (Momentum Engine)", style="default", title_style="bold green")
            table.add_column("方向", justify="center")
            table.add_column("交易对", style="cyan")
            table.add_column("强度分", justify="right")
            table.add_column("ADX", justify="right")
            table.add_column("风险提示", justify="left")
            for _, row in longs.iterrows():
                risk = "🔥费率>0.05%" if row['funding_rate'] > 0.05 else ""
                table.add_row("[bold green]做多[/]", row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.1f}", risk)
            if not longs.empty and not shorts.empty: table.add_section()
            for _, row in shorts.iterrows():
                risk = "🧊费率<-0.05%" if row['funding_rate'] < -0.05 else ""
                table.add_row("[bold red]做空[/]", row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.1f}", risk)
            console.print(table)

        # --- 3. 配对交易池 ---
        if correlation_matrix is not None:
            long_cands = momentum_df.head(5)
            short_cands = momentum_df.tail(5)
            pairs = []
            for _, l_row in long_cands.iterrows():
                for _, s_row in short_cands.iterrows():
                    l_sym, s_sym = l_row['symbol'], s_row['symbol']
                    if l_sym in correlation_matrix and s_sym in correlation_matrix.columns:
                        corr = correlation_matrix.loc[l_sym, s_sym]
                        if corr < 0.4: # 寻找低相关性的配对
                            pairs.append((l_row, s_row, corr))
            if pairs:
                pairs.sort(key=lambda x: x[0]['strength'] - x[1]['strength'], reverse=True)
                table = Table(title="⚖️ 配对交易策略池", style="default", title_style="bold yellow")
                table.add_column("多头候选", justify="left")
                table.add_column("空头候选", justify="left")
                table.add_column("强度差", justify="right")
                table.add_column("相关性", justify="right")
                for l, s, corr in pairs[:5]:
                    table.add_row(f"[green]{l['symbol']}[/]({l['strength']:.2f})", f"[red]{s['symbol']}[/]({s['strength']:.2f})", f"[bold]{l['strength']-s['strength']:.2f}[/bold]", f"{corr:.3f}")
                console.print(table)

async def send_unified_feishu_notification(market_state, momentum_df, correlation_matrix, extremity_df):
    """构建并发送一个统一的、包含所有关键信息的飞书卡片消息。"""
    webhook_url = CONFIG.get('feishu_webhook_url')
    if not webhook_url or 'YOUR_WEBHOOK_URL_HERE' in webhook_url: return

    def text_div(content): return {"tag": "div", "text": {"content": content, "tag": "lark_md"}}
    def hr(): return {"tag": "hr"}
    
    elements = []
    
    # --- 1. 焦点信号部分 ---
    top_long = momentum_df[momentum_df['strength'] > 1.5].iloc[0] if not momentum_df[momentum_df['strength'] > 1.5].empty else None
    top_reversal = extremity_df.iloc[0] if not extremity_df.empty else None
    focus_text = "**今日焦点**：\n"
    has_focus = False
    if top_long is not None:
        focus_text += f"- 强势做多关注 **{top_long['symbol']}** (强度: {top_long['strength']:.2f})\n"
        has_focus = True
    if top_reversal is not None:
        focus_text += f"- 底部反转关注 **{top_reversal['symbol']}** (极端分: {top_reversal['total_score']})\n"
        has_focus = True
    if not has_focus:
        focus_text = "**今日焦点**：市场信号纷杂，建议谨慎观察。"
    elements.append(text_div(focus_text))
    elements.append(hr())

    # --- 2. 反转池部分 ---
    if not extremity_df.empty:
        elements.append(text_div("🚨 **反转策略池 (超卖/背离)**"))
        for _, row in extremity_df.head(3).iterrows(): # 只显示前3个
            elements.append(text_div(f"- **{row['symbol']}** | **得分: {row['total_score']}** | 原因: {row['reasons']}"))
        elements.append(hr())
            
    # --- 3. 趋势池部分 ---
    longs = momentum_df[momentum_df['strength'] > 1.0].head(3)
    shorts = momentum_df[momentum_df['strength'] < -1.0].tail(3).sort_values('strength')
    if not longs.empty or not shorts.empty:
        elements.append(text_div("🚀 **趋势策略池 (动量)**"))
        for _, row in longs.iterrows():
            elements.append(text_div(f"- 🟢 **做多**: {row['symbol']} (强度: {row['strength']:.2f})"))
        for _, row in shorts.iterrows():
            elements.append(text_div(f"- 🔴 **做空**: {row['symbol']} (强度: {row['strength']:.2f})"))
        elements.append(hr())
    
    # --- 构建最终卡片 ---
    card = {
        "msg_type": "interactive",
        "card": {
            "config": {"wide_screen_mode": True},
            "header": {
                "title": {"content": f"📈 市场扫描仪 V4 | {market_state['status']}", "tag": "plain_text"},
                "template": "blue" if market_state['score'] >= 40 else ("red" if market_state['score'] <= -40 else "grey")
            },
            "elements": elements
        }
    }
    
    try:
        # 使用run_in_executor在线程池中运行阻塞的requests调用，避免阻塞事件循环
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, lambda: requests.post(webhook_url, headers={'Content-Type': 'application/json'}, data=json.dumps(card)))
        console.log("飞书通知已成功发送。")
    except Exception as e:
        console.log(f"[red]发送飞书通知失败: {e}[/red]")

# ==============================================================================
# === 5. 主程序调度器 ===
# ==============================================================================
async def run_realtime_monitor():
    """主调度函数，负责初始化、循环执行扫描和报告。"""
    exchange = ccxt.pro.binance({
        'enableRateLimit': True,
        'options': {'defaultType': 'future', 'adjustForTimeDifference': True}
    })

    # 在循环开始前，执行一次性的校准程序
    await calibrate_extremity_engine(exchange)

    try:
        while True:
            scan_start_time = datetime.now()
            console.print(Rule(f"[bold yellow]开始新一轮扫描 @ {scan_start_time.strftime('%Y-%m-%d %H:%M:%S')}", style="yellow"))

            # --- 1. 运行动量引擎，它会返回top_symbols列表 ---
            momentum_df, correlation_matrix, top_symbols = await run_momentum_scan(exchange)
            if top_symbols is None: top_symbols = []

            # --- 2. 并发运行市场状态和极端值引擎 ---
            status_task = asyncio.create_task(get_market_status(exchange, top_symbols))
            extremity_task = asyncio.create_task(run_extremity_scan(exchange))
            
            market_state = await status_task
            extremity_df = await extremity_task

            # --- 3. 统一报告 ---
            display_all_pools(market_state, momentum_df, correlation_matrix, extremity_df)
            await send_unified_feishu_notification(market_state, momentum_df, correlation_matrix, extremity_df)

            # --- 4. 等待下一个扫描周期 ---
            console.log(f"\n本轮扫描完成。等待 {CONFIG['scan_interval_seconds']} 秒后开始下一轮...")
            await asyncio.sleep(CONFIG['scan_interval_seconds'])

    except KeyboardInterrupt:
        console.log("\n监测器被用户手动停止。")
    except Exception as e:
        console.log(f"\n[bold red]发生意外的严重错误: {e}[/bold red]")
    finally:
        await exchange.close()
        console.log("交易所连接已关闭。程序退出。")

if __name__ == "__main__":
    try:
        asyncio.run(run_realtime_monitor())
    except Exception as e:
        console.print(f"[bold red]主程序执行循环中发生致命错误: {e}[/bold red]")