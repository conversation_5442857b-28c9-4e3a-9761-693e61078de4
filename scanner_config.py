"""
扫描器配置文件
可以根据市场条件和个人偏好调整参数
"""

from dataclasses import dataclass
from typing import List, Tuple, Dict

@dataclass
class ScannerConfig:
    """扫描器配置类"""
    
    # === 基础扫描参数 ===
    timeframes: List[str] = None
    timeframe_weights: List[float] = None
    lookback: int = 20
    top_n: int = 50  # 处理的品种数量
    scan_interval_seconds: int = 1800  # 30分钟扫描一次
    
    # === 策略阈值配置 ===
    # 趋势策略
    trend_strength_threshold: float = 1.0
    trend_adx_threshold: float = 25
    
    # 震荡/网格策略
    range_strength_range: Tuple[float, float] = (-0.5, 0.5)
    range_adx_threshold: float = 20
    
    # 配对交易
    correlation_threshold: float = 0.3
    
    # === 因子权重配置 ===
    factor_weights: Dict[str, float] = None
    
    # === 缓存配置 ===
    correlation_cache_hours: int = 4
    market_data_cache_minutes: int = 30
    
    # === 网络配置 ===
    max_retries: int = 3
    retry_delay: float = 1.0
    request_timeout: int = 30
    
    # === 通知配置 ===
    feishu_webhook_url: str = ''
    enable_notifications: bool = True
    
    # === 数据质量控制 ===
    min_volume_threshold: float = 1000000  # 最小24h成交量（USDT）
    min_data_points: int = 30  # 相关性计算最小数据点
    winsorize_percentiles: Tuple[float, float] = (0.025, 0.975)
    
    def __post_init__(self):
        """初始化默认值"""
        if self.timeframes is None:
            self.timeframes = ['4h', '1d']
        
        if self.timeframe_weights is None:
            self.timeframe_weights = [0.6, 0.4]
        
        if self.factor_weights is None:
            self.factor_weights = {
                'rel_strength_z': 0.5,   # 相对强度
                'volume_24h_z': 0.3,     # 流动性
                'funding_rate_z': -0.2,  # 资金费率（负权重）
            }

# === 预设配置 ===

def get_conservative_config() -> ScannerConfig:
    """保守配置 - 更严格的筛选条件"""
    return ScannerConfig(
        trend_strength_threshold=1.5,
        trend_adx_threshold=30,
        range_strength_range=(-0.3, 0.3),
        correlation_threshold=0.2,
        min_volume_threshold=5000000,
        top_n=30
    )

def get_aggressive_config() -> ScannerConfig:
    """激进配置 - 更宽松的筛选条件"""
    return ScannerConfig(
        trend_strength_threshold=0.8,
        trend_adx_threshold=20,
        range_strength_range=(-0.8, 0.8),
        correlation_threshold=0.4,
        min_volume_threshold=500000,
        top_n=100
    )

def get_scalping_config() -> ScannerConfig:
    """短线配置 - 适合高频交易"""
    return ScannerConfig(
        timeframes=['15m', '1h'],
        timeframe_weights=[0.7, 0.3],
        lookback=10,
        scan_interval_seconds=300,  # 5分钟扫描
        trend_strength_threshold=0.5,
        trend_adx_threshold=15,
        factor_weights={
            'rel_strength_z': 0.4,
            'volume_24h_z': 0.4,
            'funding_rate_z': -0.2,
        }
    )

def get_swing_config() -> ScannerConfig:
    """波段配置 - 适合中长线交易"""
    return ScannerConfig(
        timeframes=['1d', '4h'],
        timeframe_weights=[0.8, 0.2],
        lookback=30,
        scan_interval_seconds=3600,  # 1小时扫描
        trend_strength_threshold=1.2,
        trend_adx_threshold=25,
        correlation_cache_hours=8,
        factor_weights={
            'rel_strength_z': 0.6,
            'volume_24h_z': 0.2,
            'funding_rate_z': -0.2,
        }
    )

# === 市场条件适应性配置 ===

def get_bull_market_config() -> ScannerConfig:
    """牛市配置 - 偏向多头机会"""
    config = ScannerConfig()
    config.factor_weights = {
        'rel_strength_z': 0.6,   # 增加动量权重
        'volume_24h_z': 0.3,
        'funding_rate_z': -0.1,  # 降低资金费率权重
    }
    config.trend_strength_threshold = 0.8  # 降低多头门槛
    return config

def get_bear_market_config() -> ScannerConfig:
    """熊市配置 - 偏向空头机会和防守"""
    config = ScannerConfig()
    config.factor_weights = {
        'rel_strength_z': 0.4,
        'volume_24h_z': 0.2,
        'funding_rate_z': -0.4,  # 增加资金费率权重
    }
    config.range_strength_range = (-0.3, 0.3)  # 更保守的震荡范围
    return config

def get_sideways_market_config() -> ScannerConfig:
    """震荡市配置 - 偏向网格和配对交易"""
    config = ScannerConfig()
    config.trend_strength_threshold = 1.5  # 提高趋势门槛
    config.range_strength_range = (-0.8, 0.8)  # 扩大震荡范围
    config.correlation_threshold = 0.4  # 放宽相关性要求
    return config

# === 配置选择器 ===

CONFIG_PRESETS = {
    'default': ScannerConfig(),
    'conservative': get_conservative_config(),
    'aggressive': get_aggressive_config(),
    'scalping': get_scalping_config(),
    'swing': get_swing_config(),
    'bull_market': get_bull_market_config(),
    'bear_market': get_bear_market_config(),
    'sideways_market': get_sideways_market_config(),
}

def get_config(preset_name: str = 'default') -> ScannerConfig:
    """获取指定预设的配置"""
    return CONFIG_PRESETS.get(preset_name, ScannerConfig())

def list_available_configs() -> List[str]:
    """列出所有可用的配置预设"""
    return list(CONFIG_PRESETS.keys())