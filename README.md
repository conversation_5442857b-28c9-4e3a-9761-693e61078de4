# 加密货币市场扫描器 (优化版)

一个高效的加密货币市场机会扫描器，支持趋势、震荡和配对交易策略识别。

## ✨ 主要特性

- **多策略支持**: 趋势跟踪、震荡网格、配对交易
- **智能缓存**: 减少API调用，提升性能
- **配置预设**: 8种预设配置适应不同交易风格
- **实时监控**: 自动扫描并推送机会到飞书
- **错误恢复**: 网络重试和异常处理机制

## 🚀 快速开始

### 1. 环境验证
```bash
python quick_test.py
```

### 2. 查看可用配置
```bash
python run_scanner.py --list-configs
```

### 3. 启动扫描器
```bash
# 使用默认配置
python run_scanner.py

# 使用保守配置（推荐新手）
python run_scanner.py --config conservative

# 使用激进配置
python run_scanner.py --config aggressive
```

## 📊 配置说明

### 预设配置

| 配置名 | 适用场景 | 特点 |
|--------|----------|------|
| `default` | 日常使用 | 平衡的参数设置 |
| `conservative` | 稳健交易 | 更严格的筛选条件 |
| `aggressive` | 激进交易 | 更宽松的筛选条件 |
| `scalping` | 短线交易 | 15分钟和1小时周期 |
| `swing` | 波段交易 | 日线和4小时周期 |
| `bull_market` | 牛市环境 | 偏向多头机会 |
| `bear_market` | 熊市环境 | 偏向空头和防守 |
| `sideways_market` | 震荡市 | 偏向网格交易 |

### 自定义参数
```bash
# 自定义处理品种数量
python run_scanner.py --top-n 30

# 自定义扫描间隔（秒）
python run_scanner.py --interval 600

# 配置飞书通知
python run_scanner.py --webhook "YOUR_FEISHU_WEBHOOK_URL"
```

## 📈 策略池说明

### 🏆 趋势策略池
- **多头信号**: 强度分 > 1.0 且 ADX > 25
- **空头信号**: 强度分 < -1.0 且 ADX > 25
- **附加信息**: 回调机会、成交量放大等

### 🔀 震荡/网格策略池
- **筛选条件**: 强度分在 -0.5 到 0.5 之间
- **趋势确认**: ADX < 20（无明显趋势）
- **波动性**: 布林带宽度适中
- **建议区间**: 基于历史高低点

### ⚖️ 配对交易策略池
- **选择标准**: 强弱对比明显
- **相关性**: 低相关性（< 0.3）
- **风险控制**: 基于历史价格关系

## 🔧 高级配置

### 修改因子权重
编辑 `scanner_config.py` 中的 `factor_weights`:
```python
factor_weights = {
    'rel_strength_z': 0.5,   # 相对强度权重
    'volume_24h_z': 0.3,     # 成交量权重  
    'funding_rate_z': -0.2,  # 资金费率权重（负值）
}
```

### 调整阈值
```python
# 趋势策略阈值
trend_strength_threshold = 1.0  # 强度分阈值
trend_adx_threshold = 25        # ADX阈值

# 震荡策略阈值  
range_strength_range = (-0.5, 0.5)  # 强度分范围
range_adx_threshold = 20             # ADX上限
```

## 📱 飞书通知配置

1. 在飞书群中添加机器人
2. 获取Webhook URL
3. 配置到扫描器：
```bash
python run_scanner.py --webhook "https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_KEY"
```

## 🛠️ 故障排除

### 常见问题

**1. 网络连接失败**
```bash
# 增加重试次数
# 编辑 scanner_config.py
max_retries = 5
retry_delay = 2.0
```

**2. 扫描速度慢**
```bash
# 减少处理品种数量
python run_scanner.py --top-n 20

# 或使用更快的配置
python run_scanner.py --config scalping
```

**3. 内存使用过高**
```bash
# 减少缓存时间
# 编辑 scanner_config.py
correlation_cache_hours = 2
market_data_cache_minutes = 15
```

### 日志查看
扫描器会输出详细的运行日志，包括：
- 扫描进度
- 缓存命中情况
- 网络请求状态
- 错误信息

## 📊 性能优化建议

1. **网络优化**: 使用稳定的网络连接
2. **资源控制**: 根据机器性能调整 `top_n` 参数
3. **缓存策略**: 合理设置缓存时间
4. **监控频率**: 根据交易风格调整扫描间隔

## 🔄 版本更新

### v2.0 (当前版本)
- ✅ 重构代码架构
- ✅ 增加配置管理系统
- ✅ 优化性能和稳定性
- ✅ 增强错误处理机制
- ✅ 支持多种预设配置

### 计划功能
- 🔄 机器学习模型集成
- 🔄 更多技术指标支持
- 🔄 实时风险管理
- 🔄 策略回测功能

## 📞 支持

如果遇到问题或有改进建议，请：
1. 首先运行 `python quick_test.py` 检查环境
2. 查看日志输出定位问题
3. 参考故障排除部分

---

**免责声明**: 本工具仅供学习和研究使用，不构成投资建议。交易有风险，请谨慎决策。