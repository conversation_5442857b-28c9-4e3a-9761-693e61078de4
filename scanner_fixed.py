# ==============================================================================
# === 加密货币市场机会扫描器 V4 - 修复版 ===
# ==============================================================================

import sys
import asyncio
import json
import time
from datetime import datetime
import numpy as np
import pandas as pd
import ccxt.pro
import requests

# Rich 用于美化终端输出
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn
from rich.rule import Rule

# 修复在 Windows 上的 aiodns/asyncio 兼容性问题
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# ==============================================================================
# === 技术指标计算函数 ===
# ==============================================================================

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_ema(prices, period):
    """计算EMA指标"""
    return prices.ewm(span=period).mean()

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """计算布林带"""
    sma = prices.rolling(window=period).mean()
    std = prices.rolling(window=period).std()
    upper_band = sma + (std * std_dev)
    lower_band = sma - (std * std_dev)
    bb_percent = (prices - lower_band) / (upper_band - lower_band)
    bb_width = (upper_band - lower_band) / sma
    return upper_band, lower_band, bb_percent, bb_width

def calculate_adx(high, low, close, period=14):
    """计算ADX指标"""
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    plus_dm = high.diff()
    minus_dm = -low.diff()
    plus_dm[plus_dm < 0] = 0
    minus_dm[minus_dm < 0] = 0
    
    plus_di = 100 * (plus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
    minus_di = 100 * (minus_dm.rolling(window=period).mean() / tr.rolling(window=period).mean())
    
    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.rolling(window=period).mean()
    return adx

def calculate_mfi(high, low, close, volume, period=14):
    """计算MFI指标"""
    typical_price = (high + low + close) / 3
    money_flow = typical_price * volume
    
    positive_flow = money_flow.where(typical_price > typical_price.shift(), 0).rolling(window=period).sum()
    negative_flow = money_flow.where(typical_price < typical_price.shift(), 0).rolling(window=period).sum()
    
    mfi = 100 - (100 / (1 + positive_flow / negative_flow))
    return mfi

# ==============================================================================
# === 配置中心 ===
# ==============================================================================
CONFIG = {
    'scan_interval_seconds': 300,  # 减少到5分钟用于测试
    'top_n_by_volume': 20,  # 减少扫描数量
    'test_mode': True,  # 添加测试模式
    'feishu_webhook_url': 'https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_WEBHOOK_URL_HERE',
    
    'market_status': {
        'base_symbols': ['BTC/USDT', 'ETH/USDT'],
        'daily_ema_periods': [50, 200],
        'breadth_timeframe': '4h',
        'breadth_ema': 20,
    },
    
    'momentum_engine': {
        'timeframes': ['4h', '1d'],
        'timeframe_weights': [0.6, 0.4],
        'lookback': 20,
        'factor_weights': {
            'rel_strength_z': 0.6,
            'volume_24h_z': 0.4,
        },
        'correlation_timeframe': '1d',
        'correlation_lookback': 90,
    },
    
    'extremity_engine': {
        'enabled': True,
        'scan_symbols': ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT', 'XRP/USDT'],
        'calibration_lookback_days': 365,
        'timeframe': '1d',
        'percentiles': [0.01, 0.05, 0.10, 0.90, 0.95, 0.99],
        'factors': {
            'RSI_14': {'type': 'oscillator_low'},
            'MFI_14': {'type': 'oscillator_low'},
            'BBP_20_2.0': {'type': 'oscillator_low'},
            'price_vs_ema200_pct': {'type': 'deviation_low'},
            'BBW_20_2.0': {'type': 'volatility_squeeze'},
        },
        'score_map': {
            'low': {0.01: 40, 0.05: 20, 0.10: 10},
            'high': {0.99: 40, 0.95: 20, 0.90: 10}
        },
        'divergence_bonus': 30,
        'alert_threshold_score': 50,
    }
}

calibration_cache = {}
console = Console()

# ==============================================================================
# === 辅助函数 ===
# ==============================================================================
async def fetch_perpetual_symbols(exchange):
    """获取所有永续合约交易对"""
    for attempt in range(3):  # 重试3次
        try:
            await asyncio.sleep(attempt * 2)  # 递增延迟
            markets = await exchange.load_markets()
            perpetual_symbols = [symbol for symbol, market in markets.items() 
                               if market.get('type') == 'swap' and market.get('active', True)]
            console.log(f"[green]成功获取 {len(perpetual_symbols)} 个永续合约交易对[/green]")
            return perpetual_symbols
        except Exception as e:
            console.log(f"[yellow]获取永续合约列表失败 (尝试 {attempt+1}/3): {e}[/yellow]")
            if attempt == 2:  # 最后一次尝试失败
                console.log("[red]使用默认交易对列表[/red]")
                return ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT', 'XRP/USDT', 
                       'ADA/USDT', 'MATIC/USDT', 'LINK/USDT', 'UNI/USDT']

def find_bullish_divergence(price_series, indicator_series, lookback=30):
    """检测看涨背离"""
    try:
        price, indicator = price_series.tail(lookback), indicator_series.tail(lookback)
        if len(price) < 10 or len(indicator) < 10:
            return False
            
        price_min_idx = price.idxmin()
        indicator_at_price_min = indicator.loc[price_min_idx]
        
        earlier_data = indicator[:price_min_idx]
        if len(earlier_data) > 5:
            earlier_min = earlier_data.min()
            if indicator_at_price_min > earlier_min:
                return True
    except Exception:
        return False
    return False

# ==============================================================================
# === 核心分析引擎 ===
# ==============================================================================

async def get_market_status(exchange, top_symbols):
    """计算宏观市场状态"""
    cfg = CONFIG['market_status']
    status_score = 0
    reasons = []

    for symbol in cfg['base_symbols']:
        try:
            ohlcv = await exchange.fetch_ohlcv(symbol, '1d', limit=max(cfg['daily_ema_periods']) + 5)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            if len(df) < max(cfg['daily_ema_periods']): continue
            
            close = df['close'].iloc[-1]
            for ema_period in cfg['daily_ema_periods']:
                ema_val = calculate_ema(df['close'], ema_period).iloc[-1]
                if close > ema_val:
                    score_add = 15 if ema_period == 50 else 25
                    status_score += score_add
                    reasons.append(f"{symbol.split('/')[0]}>D_EMA{ema_period}")
        except Exception:
            continue

    try:
        tasks = [exchange.fetch_ohlcv(s, cfg['breadth_timeframe'], limit=cfg['breadth_ema'] + 2) for s in top_symbols[:20]]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        above_ema_count = 0
        valid_symbols = 0
        for res in results:
            if isinstance(res, list) and len(res) >= cfg['breadth_ema'] + 1:
                valid_symbols += 1
                df = pd.DataFrame(res, columns=['timestamp','o','h','l','close','v'])
                ema_val = calculate_ema(df['close'], cfg['breadth_ema']).iloc[-1]
                if df['close'].iloc[-1] > ema_val:
                    above_ema_count += 1
        
        if valid_symbols > 0:
            breadth_pct = (above_ema_count / valid_symbols) * 100
            reasons.append(f"广度:{breadth_pct:.0f}%")
            if breadth_pct > 60: status_score += 20
            elif breadth_pct < 40: status_score -= 20
    except Exception:
        pass

    if status_score >= 80: status = "🔥 极度看涨"
    elif status_score >= 40: status = "🟢 看涨"
    elif status_score <= -80: status = "🧊 极度看跌"
    elif status_score <= -40: status = "🔴 看跌"
    else: status = "⚪ 中性"
        
    return {'status': status, 'score': status_score, 'details': ", ".join(reasons)}

async def calibrate_extremity_engine(exchange):
    """校准极端值引擎"""
    global calibration_cache
    cfg = CONFIG['extremity_engine']
    if not cfg['enabled']: return
    console.print(Rule("[bold blue]启动极端值引擎校准程序[/bold blue]"))
    
    for symbol in cfg['scan_symbols']:
        for attempt in range(3):  # 每个symbol重试3次
            try:
                await asyncio.sleep(attempt * 1)  # 递增延迟
                ohlcv = await exchange.fetch_ohlcv(symbol, cfg['timeframe'], limit=cfg['calibration_lookback_days'])
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                
                if len(df) < 100:  # 数据不足
                    console.log(f"  ⚠️ [cyan]{symbol}[/cyan] 数据不足，跳过校准")
                    break
                    
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)

                df['RSI_14'] = calculate_rsi(df['close'], 14)
                df['MFI_14'] = calculate_mfi(df['high'], df['low'], df['close'], df['volume'], 14)
                upper_bb, lower_bb, df['BBP_20_2.0'], df['BBW_20_2.0'] = calculate_bollinger_bands(df['close'], 20, 2)
                ema200 = calculate_ema(df['close'], 200)
                df['price_vs_ema200_pct'] = (df['close'] / ema200 - 1) * 100

                symbol_thresholds = {}
                for factor_name in cfg['factors'].keys():
                    if factor_name in df.columns:
                        symbol_thresholds[factor_name] = df[factor_name].quantile(cfg['percentiles']).to_dict()
                calibration_cache[symbol] = symbol_thresholds
                console.log(f"  ✅ [cyan]{symbol}[/cyan] 的动态阈值校准成功。")
                break  # 成功则跳出重试循环
            except Exception as e:
                if attempt == 2:  # 最后一次尝试
                    console.log(f"  ❌ [cyan]{symbol}[/cyan] 的动态阈值校准失败: {e}")
                else:
                    console.log(f"  ⚠️ [cyan]{symbol}[/cyan] 校准重试 {attempt+1}/3")
                    
    console.print(Rule("[bold green]校准程序完成[/bold green]"))

async def run_extremity_scan(exchange):
    """运行极端值扫描"""
    cfg = CONFIG['extremity_engine']
    if not cfg['enabled'] or not calibration_cache: return pd.DataFrame()

    results = []
    for symbol in cfg['scan_symbols']:
        try:
            ohlcv = await exchange.fetch_ohlcv(symbol, cfg['timeframe'], limit=250)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            if len(df) < 200 or symbol not in calibration_cache: continue

            df['RSI_14'] = calculate_rsi(df['close'], 14)
            df['MFI_14'] = calculate_mfi(df['high'], df['low'], df['close'], df['volume'], 14)
            upper_bb, lower_bb, df['BBP_20_2.0'], df['BBW_20_2.0'] = calculate_bollinger_bands(df['close'], 20, 2)
            ema200 = calculate_ema(df['close'], 200)
            df['price_vs_ema200_pct'] = (df['close'] / ema200 - 1) * 100

            total_score, reasons, details = 0, [], {}
            thresholds = calibration_cache[symbol]

            for factor, f_cfg in cfg['factors'].items():
                current_value = df[factor].iloc[-1] if factor in df.columns else np.nan
                if pd.isna(current_value): continue
                details[factor] = f"{current_value:.2f}"
                if factor not in thresholds: continue

                score_type = f_cfg['type']
                score_map = cfg['score_map']
                if score_type in ['oscillator_low', 'deviation_low', 'volatility_squeeze']:
                    for p, score in score_map['low'].items():
                        if current_value < thresholds[factor][p]:
                            total_score += score
                            reasons.append(f"{factor.split('_')[0]}<{p*100}%({score})")
                            break
            
            if 'RSI_14' in df.columns and find_bullish_divergence(df['close'], df['RSI_14']):
                total_score += cfg['divergence_bonus']
                reasons.append(f"RSI看涨背离({cfg['divergence_bonus']})")
                details['Divergence'] = "RSI Bullish"

            if total_score >= cfg['alert_threshold_score']:
                results.append({'symbol': symbol, 'total_score': total_score, 'details': details, 'reasons': ", ".join(reasons)})
        except Exception:
            continue

    if not results: return pd.DataFrame()
    return pd.DataFrame(results).sort_values('total_score', ascending=False)

async def calculate_momentum_factors(exchange, symbol, btc_data_cache, cfg):
    """计算动量因子"""
    all_timeframe_results = []
    for timeframe in cfg['timeframes']:
        for attempt in range(2):  # 重试2次
            try:
                await asyncio.sleep(attempt * 0.5)  # 短暂延迟
                ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=cfg['lookback'] + 100)
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                if len(df) < cfg['lookback'] + 27: 
                    break  # 数据不足，不重试

                df['RSI_14'] = calculate_rsi(df['close'], 14)
            df[f'ADX_{cfg["lookback"]}'] = calculate_adx(df['high'], df['low'], df['close'], cfg['lookback'])
            upper_bb, lower_bb, bb_percent, df[f'BBW_{cfg["lookback"]}_2.0'] = calculate_bollinger_bands(df['close'], cfg['lookback'], 2)

            adx = df[f'ADX_{cfg["lookback"]}'].iloc[-2]
            bbw = df[f'BBW_{cfg["lookback"]}_2.0'].iloc[-2]
            rsi = df['RSI_14'].iloc[-2]
            
            momentum = (df['close'].iloc[-2] / df['close'].iloc[-cfg['lookback']-2] - 1) * 100
            
            rel_strength = momentum
            if btc_data_cache.get(timeframe) is not None:
                btc_df = btc_data_cache[timeframe]
                btc_momentum = (btc_df['close'].iloc[-2] / btc_df['close'].iloc[-cfg['lookback']-2] - 1) * 100
                rel_strength = momentum - btc_momentum
            
            funding_rate = 0.0
            try:
                funding_data = await exchange.fetch_funding_rate(symbol)
                funding_rate = funding_data.get('fundingRate', 0.0) * 100
            except Exception: pass
            
            volume_24h = 0.0
            try:
                ticker = await exchange.fetch_ticker(symbol)
                volume_24h = ticker.get('quoteVolume', 0.0)
            except Exception: pass

                all_timeframe_results.append({
                    'rel_strength': rel_strength, 'volume_24h': volume_24h,
                    'adx': adx, 'bbw': bbw, 'rsi': rsi,
                    'funding_rate': funding_rate,
                })
                break  # 成功则跳出重试循环
            except Exception as e:
                if attempt == 1:  # 最后一次尝试失败
                    continue
                await asyncio.sleep(1)  # 重试前等待
    
    if not all_timeframe_results: return None

    final_result = {}
    weights = cfg['timeframe_weights']
    for key in all_timeframe_results[0].keys():
        values = [res.get(key, 0) for res in all_timeframe_results]
        if key == 'volume_24h':
            final_result[key] = values[0]
            continue
        final_result[key] = np.average(values, weights=weights[:len(values)])
            
    final_result['symbol'] = symbol
    return final_result

async def run_momentum_scan(exchange):
    """运行动量扫描"""
    cfg = CONFIG['momentum_engine']
    all_symbols = await fetch_perpetual_symbols(exchange)
    if not all_symbols: return pd.DataFrame(), None, []

    # 如果是测试模式或网络有问题，直接使用默认列表
    if CONFIG.get('test_mode', False) or not all_symbols:
        top_symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT', 'XRP/USDT', 
                      'ADA/USDT', 'MATIC/USDT', 'LINK/USDT', 'UNI/USDT'][:CONFIG['top_n_by_volume']]
        console.log(f"[yellow]使用默认交易对列表: {len(top_symbols)} 个[/yellow]")
    else:
        try:
            all_tickers = await exchange.fetch_tickers()
            volume_df = pd.DataFrame([
                {'symbol': s, 'quoteVolume': t['quoteVolume']} 
                for s, t in all_tickers.items() if s in all_symbols and t.get('quoteVolume')
            ])
            top_symbols = volume_df.sort_values('quoteVolume', ascending=False).head(CONFIG['top_n_by_volume'])['symbol'].tolist()
            console.log(f"已筛选出交易额最高的 [bold green]{len(top_symbols)}[/bold green] 个交易对进行动量扫描。")
        except Exception as e:
            console.log(f"[red]筛选交易对失败: {e}，将使用默认列表。[/red]")
            top_symbols = [s for s in all_symbols if s.endswith('/USDT')][:CONFIG['top_n_by_volume']]

    btc_data_cache = {}
    try:
        btc_tasks = [exchange.fetch_ohlcv('BTC/USDT', tf, limit=cfg['lookback'] + 100) for tf in cfg['timeframes']]
        all_btc_ohlcv = await asyncio.gather(*btc_tasks)
        for i, tf in enumerate(cfg['timeframes']):
            btc_data_cache[tf] = pd.DataFrame(all_btc_ohlcv[i], columns=['timestamp', 'o','h','l','close','v'])
    except Exception as e:
        console.log(f"[yellow]获取BTC基准数据失败: {e}[/yellow]")

    tasks = [calculate_momentum_factors(exchange, symbol, btc_data_cache, cfg) for symbol in top_symbols]
    results = []
    with Progress(SpinnerColumn(), TextColumn("[progress.description]{task.description}"), BarColumn(), TextColumn("[progress.percentage]{task.percentage:>3.0f}%")) as progress:
        scan_task = progress.add_task("[cyan]正在扫描市场动量...", total=len(tasks))
        for future in asyncio.as_completed(tasks):
            result = await future
            if result: results.append(result)
            progress.update(scan_task, advance=1)

    if not results: return pd.DataFrame(), None, top_symbols
    df = pd.DataFrame(results).set_index('symbol')
    
    for factor in ['rel_strength', 'volume_24h']:
        p_low, p_high = df[factor].quantile(0.025), df[factor].quantile(0.975)
        df[factor] = np.clip(df[factor], p_low, p_high)
        df[f'{factor}_z'] = (df[factor] - df[factor].mean()) / df[factor].std()

    df['strength'] = sum(weight * df[factor_z] for factor_z, weight in cfg['factor_weights'].items() if factor_z in df.columns)
    df = df.sort_values('strength', ascending=False).reset_index().dropna(subset=['strength'])
    
    correlation_matrix = None
    try:
        corr_symbols = df['symbol'].tolist()[:20]
        if 'BTC/USDT' not in corr_symbols: corr_symbols.insert(0, 'BTC/USDT')
        
        corr_tasks = [exchange.fetch_ohlcv(s, cfg['correlation_timeframe'], limit=cfg['correlation_lookback']) for s in corr_symbols]
        ohlcv_results = await asyncio.gather(*corr_tasks, return_exceptions=True)
        
        close_series = {}
        for sym, res in zip(corr_symbols, ohlcv_results):
            if isinstance(res, list) and len(res) > 1:
                temp_df = pd.DataFrame(res, columns=['timestamp','o','h','l','close','v'])
                temp_df['timestamp'] = pd.to_datetime(temp_df['timestamp'], unit='ms')
                close_series[sym] = temp_df.set_index('timestamp')['close']
        
        if len(close_series) > 1:
            returns_df = pd.concat(close_series, axis=1).pct_change().dropna(how='all')
            if returns_df.shape[1] > 1:
                correlation_matrix = returns_df.corr()
    except Exception as e:
        console.log(f"[yellow]相关性矩阵计算失败: {e}[/yellow]")
        
    return df, correlation_matrix, top_symbols

# ==============================================================================
# === 报告模块 ===
# ==============================================================================
def display_mock_results():
    """显示模拟结果（离线模式）"""
    console.print(Rule("[bold]📊 市场扫描结果 (模拟数据) | 当前状态: 🟢 看涨[/bold]", style="blue"))
    
    # 模拟动量数据
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT']
    mock_data = []
    for symbol in symbols:
        strength = np.random.uniform(-2, 2)
        adx = np.random.uniform(15, 45)
        funding_rate = np.random.uniform(-0.1, 0.1)
        mock_data.append({'symbol': symbol, 'strength': strength, 'adx': adx, 'funding_rate': funding_rate})
    
    df = pd.DataFrame(mock_data).sort_values('strength', ascending=False)
    
    table = Table(title="🚀 趋势策略池 (模拟)", style="default", title_style="bold green")
    table.add_column("方向", justify="center")
    table.add_column("交易对", style="cyan")
    table.add_column("强度分", justify="right")
    table.add_column("ADX", justify="right")
    table.add_column("风险提示", justify="left")
    
    for _, row in df.iterrows():
        direction = "🟢 做多" if row['strength'] > 0 else "🔴 做空"
        risk = "🔥费率>0.05%" if row['funding_rate'] > 0.05 else ""
        table.add_row(direction, row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.1f}", risk)
    
    console.print(table)
    console.print("[dim]注意：以上为模拟数据，仅供测试使用[/dim]")

def display_all_pools(market_state, momentum_df, correlation_matrix, extremity_df):
    """显示所有分析结果"""
    console.print(Rule(f"[bold]📊 市场扫描结果 | 当前状态: {market_state['status']} ({market_state['details']})[/bold]", style="blue"))

    if not extremity_df.empty:
        table = Table(title="🚨 反转策略池 (Extremity Engine)", style="default", title_style="bold magenta")
        table.add_column("交易对", style="cyan")
        details_keys = list(extremity_df.iloc[0]['details'].keys()) if not extremity_df.empty else []
        for key in details_keys:
            table.add_column(key.split('_')[0], justify="right")
        table.add_column("总分", justify="center", style="bold yellow")
        table.add_column("得分原因", justify="left", max_width=50)
        for _, row in extremity_df.iterrows():
            row_data = [row['symbol']] + [row['details'].get(k, "N/A") for k in details_keys] + [f"{row['total_score']}", row['reasons']]
            table.add_row(*row_data)
        console.print(table)
    
    if not momentum_df.empty:
        longs = momentum_df[momentum_df['strength'] > 1.0].head(5)
        shorts = momentum_df[momentum_df['strength'] < -1.0].tail(5).sort_values('strength')
        if not longs.empty or not shorts.empty:
            table = Table(title="🚀 趋势策略池 (Momentum Engine)", style="default", title_style="bold green")
            table.add_column("方向", justify="center")
            table.add_column("交易对", style="cyan")
            table.add_column("强度分", justify="right")
            table.add_column("ADX", justify="right")
            table.add_column("风险提示", justify="left")
            for _, row in longs.iterrows():
                risk = "🔥费率>0.05%" if row['funding_rate'] > 0.05 else ""
                table.add_row("[bold green]做多[/]", row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.1f}", risk)
            if not longs.empty and not shorts.empty: table.add_section()
            for _, row in shorts.iterrows():
                risk = "🧊费率<-0.05%" if row['funding_rate'] < -0.05 else ""
                table.add_row("[bold red]做空[/]", row['symbol'], f"{row['strength']:.2f}", f"{row['adx']:.1f}", risk)
            console.print(table)

async def run_realtime_monitor():
    """主程序"""
    exchange = ccxt.pro.binance({
        'enableRateLimit': True,
        'rateLimit': 2000,  # 增加请求间隔
        'timeout': 30000,   # 增加超时时间
        'options': {
            'defaultType': 'future', 
            'adjustForTimeDifference': True,
            'recvWindow': 10000  # 增加接收窗口
        },
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    })

    # 测试连接
    try:
        await exchange.load_markets()
        console.log("[green]✅ 成功连接到币安API[/green]")
        await calibrate_extremity_engine(exchange)
    except Exception as e:
        console.log(f"[red]❌ 无法连接到币安API: {e}[/red]")
        console.log("[yellow]⚠️ 将使用离线模式运行[/yellow]")
        CONFIG['test_mode'] = True

    try:
        scan_count = 0
        while True:
            scan_count += 1
            scan_start_time = datetime.now()
            console.print(Rule(f"[bold yellow]开始第 {scan_count} 轮扫描 @ {scan_start_time.strftime('%Y-%m-%d %H:%M:%S')}", style="yellow"))

            if CONFIG.get('test_mode', False):
                # 离线模式：显示模拟数据
                console.print("[yellow]🔄 离线模式：使用模拟数据[/yellow]")
                display_mock_results()
            else:
                # 在线模式：真实API调用
                momentum_df, correlation_matrix, top_symbols = await run_momentum_scan(exchange)
                if top_symbols is None: top_symbols = []

                status_task = asyncio.create_task(get_market_status(exchange, top_symbols))
                extremity_task = asyncio.create_task(run_extremity_scan(exchange))
                
                market_state = await status_task
                extremity_df = await extremity_task

                display_all_pools(market_state, momentum_df, correlation_matrix, extremity_df)

            console.log(f"\n✅ 第 {scan_count} 轮扫描完成。等待 {CONFIG['scan_interval_seconds']} 秒后开始下一轮...")
            await asyncio.sleep(CONFIG['scan_interval_seconds'])

    except KeyboardInterrupt:
        console.log("\n监测器被用户手动停止。")
    except Exception as e:
        console.log(f"\n[bold red]发生意外的严重错误: {e}[/bold red]")
    finally:
        await exchange.close()
        console.log("交易所连接已关闭。程序退出。")

if __name__ == "__main__":
    try:
        asyncio.run(run_realtime_monitor())
    except Exception as e:
        console.print(f"[bold red]主程序执行循环中发生致命错误: {e}[/bold red]")