# 简化的加密货币市场扫描器 - 测试版
import asyncio
import sys
import pandas as pd
import numpy as np
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.rule import Rule

# Windows兼容性
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

console = Console()

# 模拟数据生成器
def generate_mock_data():
    """生成模拟的市场数据用于测试"""
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT']
    mock_data = []
    
    for symbol in symbols:
        # 生成随机的技术指标数据
        rsi = np.random.uniform(20, 80)
        strength = np.random.uniform(-2, 2)
        adx = np.random.uniform(10, 50)
        funding_rate = np.random.uniform(-0.1, 0.1)
        
        mock_data.append({
            'symbol': symbol,
            'strength': strength,
            'rsi': rsi,
            'adx': adx,
            'funding_rate': funding_rate
        })
    
    return pd.DataFrame(mock_data)

def display_test_results():
    """显示测试结果"""
    console.print(Rule("[bold blue]市场扫描器测试模式[/bold blue]"))
    
    # 生成模拟数据
    df = generate_mock_data()
    df = df.sort_values('strength', ascending=False)
    
    # 显示结果表格
    table = Table(title="🚀 模拟趋势策略池", style="default", title_style="bold green")
    table.add_column("方向", justify="center")
    table.add_column("交易对", style="cyan")
    table.add_column("强度分", justify="right")
    table.add_column("RSI", justify="right")
    table.add_column("ADX", justify="right")
    table.add_column("资金费率", justify="right")
    
    for _, row in df.iterrows():
        direction = "🟢 做多" if row['strength'] > 0 else "🔴 做空"
        table.add_row(
            direction,
            row['symbol'],
            f"{row['strength']:.2f}",
            f"{row['rsi']:.1f}",
            f"{row['adx']:.1f}",
            f"{row['funding_rate']:.3f}%"
        )
    
    console.print(table)
    
    # 显示市场状态
    market_score = np.random.randint(-50, 50)
    if market_score > 20:
        status = "🟢 看涨"
    elif market_score < -20:
        status = "🔴 看跌"
    else:
        status = "⚪ 中性"
    
    console.print(f"\n[bold]当前市场状态: {status} (得分: {market_score})[/bold]")

async def main():
    """主程序"""
    console.print("[bold yellow]启动加密货币市场扫描器测试模式...[/bold yellow]")
    console.print("[yellow]注意: 这是测试模式，使用模拟数据[/yellow]\n")
    
    try:
        while True:
            scan_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            console.print(Rule(f"[bold]扫描时间: {scan_time}[/bold]"))
            
            display_test_results()
            
            console.print(f"\n[green]扫描完成。60秒后进行下一轮扫描...[/green]")
            console.print("[dim]按 Ctrl+C 停止程序[/dim]\n")
            
            await asyncio.sleep(60)  # 1分钟间隔
            
    except KeyboardInterrupt:
        console.print("\n[yellow]程序被用户停止。[/yellow]")
    except Exception as e:
        console.print(f"\n[red]程序出错: {e}[/red]")

if __name__ == "__main__":
    asyncio.run(main())