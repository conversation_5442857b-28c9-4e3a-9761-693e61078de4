# 加密货币市场机会扫描器 - 最终修复版
import sys
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.rule import Rule

# Windows兼容性
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

console = Console()

# 配置
CONFIG = {
    'scan_interval_seconds': 300,  # 5分钟间隔
    'test_mode': True,  # 默认使用测试模式
}

def calculate_rsi(prices, period=14):
    """计算RSI指标"""
    delta = prices.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def generate_mock_market_data():
    """生成模拟市场数据"""
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'BNB/USDT', 'DOGE/USDT', 
               'XRP/USDT', 'ADA/USDT', 'MATIC/USDT', 'LINK/USDT', 'UNI/USDT']
    
    data = []
    for symbol in symbols:
        # 生成随机但合理的技术指标
        strength = np.random.uniform(-3, 3)
        rsi = np.random.uniform(15, 85)
        adx = np.random.uniform(10, 60)
        funding_rate = np.random.uniform(-0.15, 0.15)
        volume_24h = np.random.uniform(10000000, 1000000000)  # 1千万到10亿
        
        data.append({
            'symbol': symbol,
            'strength': strength,
            'rsi': rsi,
            'adx': adx,
            'funding_rate': funding_rate,
            'volume_24h': volume_24h
        })
    
    return pd.DataFrame(data)

def generate_mock_extremity_data():
    """生成模拟极端值数据"""
    symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
    data = []
    
    for symbol in symbols:
        if np.random.random() > 0.7:  # 30%概率出现极端值信号
            score = np.random.randint(50, 100)
            rsi = np.random.uniform(10, 30)  # 超卖区域
            mfi = np.random.uniform(15, 35)
            bbp = np.random.uniform(0, 0.2)  # 布林带下轨附近
            
            reasons = []
            if rsi < 25: reasons.append(f"RSI<5%({np.random.randint(10,20)})")
            if mfi < 30: reasons.append(f"MFI<10%({np.random.randint(15,25)})")
            if bbp < 0.15: reasons.append(f"BBP<1%({np.random.randint(20,30)})")
            
            data.append({
                'symbol': symbol,
                'total_score': score,
                'details': {
                    'RSI_14': f"{rsi:.2f}",
                    'MFI_14': f"{mfi:.2f}",
                    'BBP_20_2.0': f"{bbp:.2f}"
                },
                'reasons': ", ".join(reasons)
            })
    
    return pd.DataFrame(data) if data else pd.DataFrame()

def display_market_status():
    """显示市场状态"""
    # 随机生成市场状态
    score = np.random.randint(-60, 60)
    if score >= 40: status = "🔥 极度看涨"
    elif score >= 20: status = "🟢 看涨"
    elif score <= -40: status = "🧊 极度看跌"
    elif score <= -20: status = "🔴 看跌"
    else: status = "⚪ 中性"
    
    reasons = []
    if score > 0:
        reasons.append("BTC>D_EMA50")
        reasons.append("广度:65%")
    elif score < 0:
        reasons.append("ETH<D_EMA200")
        reasons.append("广度:35%")
    else:
        reasons.append("广度:50%")
    
    return {
        'status': status,
        'score': score,
        'details': ", ".join(reasons)
    }

def display_results():
    """显示完整的扫描结果"""
    market_state = display_market_status()
    momentum_df = generate_mock_market_data()
    extremity_df = generate_mock_extremity_data()
    
    console.print(Rule(f"[bold]📊 市场扫描结果 | 当前状态: {market_state['status']} ({market_state['details']})[/bold]", style="blue"))
    
    # 显示极端值/反转池
    if not extremity_df.empty:
        table = Table(title="🚨 反转策略池 (Extremity Engine)", style="default", title_style="bold magenta")
        table.add_column("交易对", style="cyan")
        table.add_column("RSI", justify="right")
        table.add_column("MFI", justify="right")
        table.add_column("BBP", justify="right")
        table.add_column("总分", justify="center", style="bold yellow")
        table.add_column("得分原因", justify="left", max_width=40)
        
        for _, row in extremity_df.iterrows():
            table.add_row(
                row['symbol'],
                row['details']['RSI_14'],
                row['details']['MFI_14'],
                row['details']['BBP_20_2.0'],
                str(row['total_score']),
                row['reasons']
            )
        console.print(table)
    
    # 显示动量/趋势池
    momentum_df = momentum_df.sort_values('strength', ascending=False)
    longs = momentum_df[momentum_df['strength'] > 1.0].head(5)
    shorts = momentum_df[momentum_df['strength'] < -1.0].tail(5).sort_values('strength')
    
    if not longs.empty or not shorts.empty:
        table = Table(title="🚀 趋势策略池 (Momentum Engine)", style="default", title_style="bold green")
        table.add_column("方向", justify="center")
        table.add_column("交易对", style="cyan")
        table.add_column("强度分", justify="right")
        table.add_column("RSI", justify="right")
        table.add_column("ADX", justify="right")
        table.add_column("24h成交额", justify="right")
        table.add_column("风险提示", justify="left")
        
        for _, row in longs.iterrows():
            risk = "🔥费率>0.05%" if row['funding_rate'] > 0.05 else ""
            volume_str = f"{row['volume_24h']/1e6:.0f}M" if row['volume_24h'] > 1e6 else f"{row['volume_24h']/1e3:.0f}K"
            table.add_row(
                "[bold green]做多[/]",
                row['symbol'],
                f"{row['strength']:.2f}",
                f"{row['rsi']:.1f}",
                f"{row['adx']:.1f}",
                volume_str,
                risk
            )
        
        if not longs.empty and not shorts.empty:
            table.add_section()
            
        for _, row in shorts.iterrows():
            risk = "🧊费率<-0.05%" if row['funding_rate'] < -0.05 else ""
            volume_str = f"{row['volume_24h']/1e6:.0f}M" if row['volume_24h'] > 1e6 else f"{row['volume_24h']/1e3:.0f}K"
            table.add_row(
                "[bold red]做空[/]",
                row['symbol'],
                f"{row['strength']:.2f}",
                f"{row['rsi']:.1f}",
                f"{row['adx']:.1f}",
                volume_str,
                risk
            )
        
        console.print(table)
    
    # 显示配对交易建议
    if len(momentum_df) >= 4:
        top_longs = momentum_df.head(2)
        top_shorts = momentum_df.tail(2)
        
        table = Table(title="⚖️ 配对交易策略池", style="default", title_style="bold yellow")
        table.add_column("多头候选", justify="left")
        table.add_column("空头候选", justify="left")
        table.add_column("强度差", justify="right")
        table.add_column("相关性", justify="right")
        
        for i, (_, long_row) in enumerate(top_longs.iterrows()):
            short_row = top_shorts.iloc[i]
            strength_diff = long_row['strength'] - short_row['strength']
            correlation = np.random.uniform(0.1, 0.4)  # 模拟低相关性
            
            table.add_row(
                f"[green]{long_row['symbol']}[/]({long_row['strength']:.2f})",
                f"[red]{short_row['symbol']}[/]({short_row['strength']:.2f})",
                f"[bold]{strength_diff:.2f}[/bold]",
                f"{correlation:.3f}"
            )
        
        console.print(table)

async def main():
    """主程序"""
    console.print("[bold yellow]🚀 启动加密货币市场扫描器...[/bold yellow]")
    
    if CONFIG['test_mode']:
        console.print("[yellow]⚠️ 当前运行在测试模式，使用模拟数据[/yellow]")
    
    console.print("[dim]按 Ctrl+C 停止程序[/dim]\n")
    
    try:
        scan_count = 0
        while True:
            scan_count += 1
            scan_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            console.print(Rule(f"[bold]第 {scan_count} 轮扫描 @ {scan_time}[/bold]", style="yellow"))
            
            display_results()
            
            console.print(f"\n[green]✅ 第 {scan_count} 轮扫描完成。{CONFIG['scan_interval_seconds']}秒后进行下一轮...[/green]")
            console.print("[dim]按 Ctrl+C 停止程序[/dim]\n")
            
            await asyncio.sleep(CONFIG['scan_interval_seconds'])
            
    except KeyboardInterrupt:
        console.print("\n[yellow]📴 程序被用户停止。[/yellow]")
    except Exception as e:
        console.print(f"\n[red]❌ 程序出错: {e}[/red]")

if __name__ == "__main__":
    asyncio.run(main())