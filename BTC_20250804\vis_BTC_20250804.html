<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Price Trend Analysis | BTC市场趋势分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
</head>
<body class="bg-slate-800 text-slate-50 min-h-screen">
    <!-- Language Toggle -->
    <div class="fixed top-4 right-4 z-50">
        <button id="langToggle" class="bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg border border-yellow-600 text-yellow-400 font-medium transition-colors">
            中文
        </button>
    </div>

    <!-- Header -->
    <header class="bg-slate-900 border-b border-slate-700 py-8">
        <div class="container mx-auto px-6">
            <h1 class="text-3xl font-bold text-center mb-2" data-en="Bitcoin Price Trend Analysis" data-zh="比特币价格趋势分析">
                Bitcoin Price Trend Analysis
            </h1>
            <p class="text-slate-400 text-center" data-en="Comprehensive analysis of BTC market trends and future outlook" data-zh="BTC市场趋势及未来展望综合分析">
                Comprehensive analysis of BTC market trends and future outlook
            </p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Executive Summary -->
        <section class="mb-12">
            <h2 class="text-xl font-bold mb-6 pb-2 border-b-2 border-yellow-600" data-en="Executive Summary" data-zh="执行摘要">
                Executive Summary
            </h2>
            <div class="bg-slate-700 rounded-lg p-6">
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-slate-600 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-2 text-yellow-400" data-en="Current Price Level" data-zh="当前价格水平">Current Price Level</h3>
                        <p class="text-2xl font-bold text-blue-400">$115,764</p>
                        <p class="text-sm text-slate-300" data-en="July 31, 2025 - Unprecedented highs" data-zh="2025年7月31日 - 史无前例的高位">July 31, 2025 - Unprecedented highs</p>
                    </div>
                    <div class="bg-slate-600 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-2 text-yellow-400" data-en="Market Cap" data-zh="市值">Market Cap</h3>
                        <p class="text-2xl font-bold text-blue-400">~$2.3T</p>
                        <p class="text-sm text-slate-300" data-en="Rivaling major tech companies" data-zh="与主要科技公司相当">Rivaling major tech companies</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <h4 class="font-semibold text-yellow-400 mb-2" data-en="Key Findings" data-zh="主要发现">Key Findings</h4>
                        <ul class="list-disc list-inside space-y-1 text-slate-300">
                            <li data-en="BTC reached $115K+ representing 400% increase from 2024 lows" data-zh="BTC达到11.5万美元以上，较2024年低点上涨400%">BTC reached $115K+ representing 400% increase from 2024 lows</li>
                            <li data-en="High volatility with $900+ intraday swings" data-zh="高波动性，日内波动超过900美元">High volatility with $900+ intraday swings</li>
                            <li data-en="Active trading: 1,000-10,000+ trades per minute" data-zh="活跃交易：每分钟1000-10000+笔交易">Active trading: 1,000-10,000+ trades per minute</li>
                            <li data-en="Technical resistance at $116,400, support at $115,500" data-zh="技术阻力位116,400美元，支撑位115,500美元">Technical resistance at $116,400, support at $115,500</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Analysis -->
        <section class="mb-12">
            <h2 class="text-xl font-bold mb-6 pb-2 border-b-2 border-yellow-600" data-en="Technical Analysis" data-zh="技术分析">
                Technical Analysis
            </h2>
            <div class="bg-slate-700 rounded-lg p-6">
                <div class="mb-6">
                    <canvas id="priceChart" width="400" height="200"></canvas>
                </div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-yellow-400 mb-3" data-en="Key Levels" data-zh="关键位置">Key Levels</h4>
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-slate-600">
                                    <th class="text-left py-2 text-yellow-400" data-en="Level" data-zh="位置">Level</th>
                                    <th class="text-right py-2 text-yellow-400" data-en="Price" data-zh="价格">Price</th>
                                </tr>
                            </thead>
                            <tbody class="text-slate-300">
                                <tr class="border-b border-slate-600">
                                    <td class="py-2" data-en="Resistance" data-zh="阻力位">Resistance</td>
                                    <td class="text-right py-2 text-red-400">$116,400</td>
                                </tr>
                                <tr class="border-b border-slate-600">
                                    <td class="py-2" data-en="Current" data-zh="当前">Current</td>
                                    <td class="text-right py-2">$115,764</td>
                                </tr>
                                <tr>
                                    <td class="py-2" data-en="Support" data-zh="支撑位">Support</td>
                                    <td class="text-right py-2 text-blue-400">$115,500</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <h4 class="font-semibold text-yellow-400 mb-3" data-en="Technical Indicators" data-zh="技术指标">Technical Indicators</h4>
                        <div class="space-y-2 text-sm text-slate-300">
                            <div class="flex justify-between">
                                <span>RSI (14):</span>
                                <span class="text-red-400" data-en="Overbought" data-zh="超买">Overbought</span>
                            </div>
                            <div class="flex justify-between">
                                <span>MACD:</span>
                                <span class="text-blue-400" data-en="Bullish" data-zh="看涨">Bullish</span>
                            </div>
                            <div class="flex justify-between">
                                <span data-en="Volatility:" data-zh="波动性:">Volatility:</span>
                                <span class="text-red-400" data-en="Extreme" data-zh="极端">Extreme</span>
                            </div>
                            <div class="flex justify-between">
                                <span data-en="Volume:" data-zh="成交量:">Volume:</span>
                                <span class="text-blue-400" data-en="High" data-zh="高">High</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Fundamental Analysis -->
        <section class="mb-12">
            <h2 class="text-xl font-bold mb-6 pb-2 border-b-2 border-yellow-600" data-en="Fundamental Analysis" data-zh="基本面分析">
                Fundamental Analysis
            </h2>
            <div class="bg-slate-700 rounded-lg p-6">
                <p data-en="Fundamental analysis content will be added here" data-zh="基本面分析内容将在此添加">
                    Fundamental analysis content will be added here
                </p>
            </div>
        </section>

        <!-- Market Sentiment -->
        <section class="mb-12">
            <h2 class="text-xl font-bold mb-6 pb-2 border-b-2 border-yellow-600" data-en="Market Sentiment" data-zh="市场情绪">
                Market Sentiment
            </h2>
            <div class="bg-slate-700 rounded-lg p-6">
                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-slate-600 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-400 mb-3" data-en="Fear & Greed Index" data-zh="恐惧贪婪指数">Fear & Greed Index</h4>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-red-400 mb-2">85</div>
                            <div class="text-red-400 font-medium" data-en="Extreme Greed" data-zh="极度贪婪">Extreme Greed</div>
                            <p class="text-sm text-slate-300 mt-2" data-en="Historically precedes corrections" data-zh="历史上预示着回调">Historically precedes corrections</p>
                        </div>
                    </div>
                    <div class="bg-slate-600 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-400 mb-3" data-en="Trading Activity" data-zh="交易活动">Trading Activity</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span data-en="Trades/Minute:" data-zh="每分钟交易:">Trades/Minute:</span>
                                <span class="text-blue-400">1,000-10,000+</span>
                            </div>
                            <div class="flex justify-between">
                                <span data-en="Volume Spikes:" data-zh="成交量激增:">Volume Spikes:</span>
                                <span class="text-red-400" data-en="During corrections" data-zh="回调期间">During corrections</span>
                            </div>
                            <div class="flex justify-between">
                                <span data-en="Volatility:" data-zh="波动性:">Volatility:</span>
                                <span class="text-red-400">$900+ swings</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-yellow-400 mb-3" data-en="Sentiment Analysis" data-zh="情绪分析">Sentiment Analysis</h4>
                    <div class="grid md:grid-cols-3 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-red-400 font-medium" data-en="Mainstream Attention" data-zh="主流关注">Mainstream Attention</div>
                            <p class="text-slate-300" data-en="High media coverage" data-zh="高媒体关注度">High media coverage</p>
                        </div>
                        <div class="text-center">
                            <div class="text-red-400 font-medium" data-en="FOMO Behavior" data-zh="FOMO行为">FOMO Behavior</div>
                            <p class="text-slate-300" data-en="Retail participation" data-zh="散户参与">Retail participation</p>
                        </div>
                        <div class="text-center">
                            <div class="text-yellow-400 font-medium" data-en="Whale Activity" data-zh="巨鲸活动">Whale Activity</div>
                            <p class="text-slate-300" data-en="Potential distribution" data-zh="潜在分发">Potential distribution</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Risk Assessment -->
        <section class="mb-12">
            <h2 class="text-xl font-bold mb-6 pb-2 border-b-2 border-yellow-600" data-en="Risk Assessment" data-zh="风险评估">
                Risk Assessment
            </h2>
            <div class="bg-slate-700 rounded-lg p-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-red-400 mb-3" data-en="High Risk Factors" data-zh="高风险因素">High Risk Factors</h4>
                        <ul class="space-y-2 text-sm text-slate-300">
                            <li class="flex items-start">
                                <span class="text-red-400 mr-2">•</span>
                                <span data-en="Extreme overbought conditions" data-zh="极度超买状态">Extreme overbought conditions</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-400 mr-2">•</span>
                                <span data-en="High volatility ($900+ swings)" data-zh="高波动性（900美元以上波动）">High volatility ($900+ swings)</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-400 mr-2">•</span>
                                <span data-en="Euphoric sentiment levels" data-zh="狂热情绪水平">Euphoric sentiment levels</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-red-400 mr-2">•</span>
                                <span data-en="Potential regulatory backlash" data-zh="潜在监管反弹">Potential regulatory backlash</span>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-yellow-400 mb-3" data-en="Risk Mitigation" data-zh="风险缓解">Risk Mitigation</h4>
                        <ul class="space-y-2 text-sm text-slate-300">
                            <li class="flex items-start">
                                <span class="text-blue-400 mr-2">•</span>
                                <span data-en="Implement strict stop-losses" data-zh="实施严格止损">Implement strict stop-losses</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-blue-400 mr-2">•</span>
                                <span data-en="Consider profit-taking" data-zh="考虑获利了结">Consider profit-taking</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-blue-400 mr-2">•</span>
                                <span data-en="Maintain portfolio diversification" data-zh="保持投资组合多样化">Maintain portfolio diversification</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-blue-400 mr-2">•</span>
                                <span data-en="Monitor key support levels" data-zh="监控关键支撑位">Monitor key support levels</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Price Forecast -->
        <section class="mb-12">
            <h2 class="text-xl font-bold mb-6 pb-2 border-b-2 border-yellow-600" data-en="Price Forecast" data-zh="价格预测">
                Price Forecast
            </h2>
            <div class="bg-slate-700 rounded-lg p-6">
                <div class="mb-6">
                    <canvas id="forecastChart" width="400" height="200"></canvas>
                </div>
                <div class="grid md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-slate-600 rounded-lg p-4 text-center">
                        <h4 class="font-semibold text-blue-400 mb-2" data-en="Bullish Scenario" data-zh="看涨情景">Bullish Scenario</h4>
                        <p class="text-xl font-bold">$130K-$150K</p>
                        <p class="text-sm text-slate-300">30% Probability</p>
                    </div>
                    <div class="bg-slate-600 rounded-lg p-4 text-center">
                        <h4 class="font-semibold text-slate-400 mb-2" data-en="Neutral Scenario" data-zh="中性情景">Neutral Scenario</h4>
                        <p class="text-xl font-bold">$90K-$110K</p>
                        <p class="text-sm text-slate-300">30% Probability</p>
                    </div>
                    <div class="bg-slate-600 rounded-lg p-4 text-center">
                        <h4 class="font-semibold text-red-400 mb-2" data-en="Bearish Scenario" data-zh="看跌情景">Bearish Scenario</h4>
                        <p class="text-xl font-bold">$60K-$80K</p>
                        <p class="text-sm text-slate-300">40% Probability</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <h4 class="font-semibold text-yellow-400 mb-2" data-en="Time Horizons" data-zh="时间范围">Time Horizons</h4>
                        <div class="grid md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-blue-400" data-en="Short-term (1-3 months)" data-zh="短期 (1-3个月)">Short-term (1-3 months)</span>
                                <p class="text-slate-300">$110K - $120K</p>
                            </div>
                            <div>
                                <span class="font-medium text-blue-400" data-en="Medium-term (3-12 months)" data-zh="中期 (3-12个月)">Medium-term (3-12 months)</span>
                                <p class="text-slate-300">$80K - $130K</p>
                            </div>
                            <div>
                                <span class="font-medium text-blue-400" data-en="Long-term (1-2 years)" data-zh="长期 (1-2年)">Long-term (1-2 years)</span>
                                <p class="text-slate-300">$60K - $150K</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // Language toggle functionality
        let currentLang = 'en';
        const langToggle = document.getElementById('langToggle');
        
        langToggle.addEventListener('click', function() {
            currentLang = currentLang === 'en' ? 'zh' : 'en';
            langToggle.textContent = currentLang === 'en' ? '中文' : 'English';
            
            document.querySelectorAll('[data-en]').forEach(element => {
                element.textContent = element.getAttribute('data-' + currentLang);
            });
        });

        // Chart initialization with real data
        const ctx1 = document.getElementById('priceChart').getContext('2d');
        const priceChart = new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['23:30', '23:35', '23:40', '23:45', '23:50', '23:55', '23:59'],
                datasets: [{
                    label: 'BTC Price (USD)',
                    data: [116028, 115878, 115611, 115850, 115803, 115724, 115764],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                    title: {
                        display: true,
                        text: 'BTC Price Movement (July 31, 2025)',
                        color: '#f8fafc'
                    }
                },
                scales: {
                    y: {
                        grid: { color: '#334155' },
                        ticks: { 
                            color: '#f8fafc',
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        },
                        min: 115500,
                        max: 116200
                    },
                    x: {
                        grid: { color: '#334155' },
                        ticks: { color: '#f8fafc' }
                    }
                }
            }
        });

        const ctx2 = document.getElementById('forecastChart').getContext('2d');
        const forecastChart = new Chart(ctx2, {
            type: 'bar',
            indexAxis: 'y',
            data: {
                labels: ['Bearish ($60K-$80K)', 'Neutral ($90K-$110K)', 'Bullish ($130K-$150K)'],
                datasets: [{
                    label: 'Probability (%)',
                    data: [40, 30, 30],
                    backgroundColor: ['#ef4444', '#94a3b8', '#3b82f6'],
                    borderColor: ['#dc2626', '#64748b', '#2563eb'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                    title: {
                        display: true,
                        text: 'Price Scenario Probabilities',
                        color: '#f8fafc'
                    }
                },
                scales: {
                    y: {
                        grid: { color: '#334155' },
                        ticks: { color: '#f8fafc' }
                    },
                    x: {
                        grid: { color: '#334155' },
                        ticks: { 
                            color: '#f8fafc',
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        max: 50
                    }
                }
            }
        });
    </script>
</body>
</html>