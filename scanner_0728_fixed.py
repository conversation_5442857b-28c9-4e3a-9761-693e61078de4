# ==============================================================================
# === 加密货币市场机会扫描器 V4 - 修复版 ===
# ==============================================================================
# 作者: [您的名字]
# 版本: 4.0.1
# 描述: 一个全天候、双引擎的市场扫描仪，结合了宏观判断、动量趋势跟踪和
#       极端值反转捕捉，并将结果统一输出到终端和飞书。
# ==============================================================================

# --- 核心依赖 ---
import sys
import asyncio
import json
import time
from datetime import datetime

# --- 第三方库 ---
import numpy as np
import pandas as pd
import ccxt.pro
import requests

# --- Rich 用于美化终端输出 ---
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn
from rich.rule import Rule

# ==============================================================================
# === 0. 初始化设置与兼容性修复 ===
# ==============================================================================

# 修复在 Windows 上的 aiodns/asyncio 兼容性问题
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# ==============================================================================
# === 1. 技术指标计算函数 ===
#red]")}[/bold 生致命错误: {eed]主程序执行循环中发f"[bold rsole.print(    con
    on as e:cepti   except Ex))
 me_monitor(tialreio.run(run_       asynctry:
 
    _main__":e__ == "_f __nam

i")程序退出。连接已关闭。交易所("onsole.log c()
       hange.close  await excly:
      nal]")
    firedd e}[/bol严重错误: {d]发生意外的\n[bold re"sole.log(f con     e:
   asion pt Except
    exce")器被用户手动停止。n监测"\le.log(conso       
 pt:terrurdInyboa Keptexce])

    val_seconds'ntercan_iCONFIG['scio.sleep(it asyn        awa)
    ..."始下一轮conds']} 秒后开al_se'scan_intervCONFIG[轮扫描完成。等待 {\n本g(f"loonsole.         c ---
   待下一个扫描周期# --- 4. 等      

      y_df)mittrix, extreion_maorrelatm_df, cntu_state, momearketcation(mfiotihu_nis_unified_fesend     await      y_df)
  tremitrix, exn_matrelationtum_df, cor, momeet_statearkls(may_all_poo    displ      告 ---
  -- 3. 统一报        # -    ity_task

trem await exf =extremity_d          tus_task
  await sta= arket_state    m 
                    ))
(exchangeanxtremity_scun_ee_task(ryncio.creatas= ask xtremity_t    e        )
symbols)ange, top_exchatus(_stt_marketgecreate_task(cio.ask = asyntatus_t         s ---
   运行市场状态和极端值引擎. 并发--- 2    # 
        ]
symbols = [op_one: ts Nsymbols i   if top_
         (exchange)scann_momentum_ait rubols = awop_sym_matrix, trelationm_df, cor momentu         ---
  symbols列表 p_会返回to引擎，它- 1. 运行动量--       # 

     ))yellow"yle=" stM:%S')}", %H:%('%Y-%m-%dtimetrfe.san_start_tim轮扫描 @ {sc始新一 yellow]开le(f"[bolde.print(Ru     consol  w()
     datetime.noe = rt_tim   scan_sta        
 e True:       whil
    try:
 change)
y_engine(exemit_extratecalibr
    await ，执行一次性的校准程序在循环开始前
    # 
    })
ue}rence': TrForTimeDiffe'adjusture', ype': 'futdefaultTons': {'       'optirue,
 imit': TateLeRabl'en    
    binance({t.pro.ge = ccxxchan"
    e""环执行扫描和报告。始化、循负责初"主调度函数，    "":
nitor()_realtime_mof run=
async de======================================================================= ======
#调度器 ===== 6. 主程序 ======
#========================================================================

# ="): {e}[/red]ed]发送飞书通知失败[re.log(f" consol        e:
n asxceptioxcept E    e。")
"飞书通知已成功发送sole.log(   con   ))
  card)on.dumps( data=json/json'},ti 'applicatent-Type':eaders={'Conok_url, ht(webhouests.pos: reqne, lambdaor(Nocutn_in_exet loop.ru awai  oop()
     ing_lt_runn= asyncio.ge loop   循环
     s调用，避免阻塞事件塞的request中运行阻ecutor在线程池 使用run_in_ex    #
      
    try:   }
          }
 elements
lements":    "e   
             },  ")
    else "greyore'] <= -40tate['scet_sed" if marklse ("r'] >= 40 ee['scorerket_statlue" if maplate": "b    "tem          xt"},
  "plain_teag": ']}", "tusstate['statmarket_ {扫描仪 V4 |": f"📈 市场 {"content"title":           ": {
     "header    ,
        ": True}_modewide_screen": {"gnfi     "co      rd": {
  "ca  ",
     ractivee": "intetyp  "msg_ {
        card =卡片 ---
  建最终# --- 构
    
    ))hr(nts.append(   eleme"))
     :.2f})'strength'](强度: {row[} bol']['symow**: {r🔴 **做空xt_div(f"- end(teents.app   elem  ():
       owsrts.iterr, row in sho    for _  )
  })")rength']:.2f'st度: {row[']} (强ow['symbol🟢 **做多**: {r"- xt_div(f(tes.append  element     s():
     ngs.iterrowlo_, row in         for *"))
势策略池 (动量)***趋"🚀 t_div((texppends.a   element    empty:
 horts.pty or not snot longs.emif   
  rength')'sts(.sort_value].tail(3)h'] < -1.0ngtreentum_df['stomntum_df[mome  shorts = m
  (3).headh'] > 1.0]strengtntum_df['tum_df[mome= momen longs 
    ---3. 趋势池部分
    # ---           nd(hr())
  appe elements.
       sons']}"))rea因: {row['re']}** | 原scootal_得分: {row['tol']}** | **['symb"- **{rowv(ft_di(texents.append    elem        : # 只显示前3个
rows()3).iterf.head(remity_d in ext for _, row
       卖/背离)**"))策略池 (超"🚨 **反转t_div(append(texts.elemen        :
ty_df.emptyxtreminot e--
    if 转池部分 -  # --- 2. 反(hr())

  ppend  elements.a))
  exts_tocuxt_div(fppend(tes.aement察。"
    el慎观，建议谨焦点**：市场信号纷杂"**今日t = focus_tex        cus:
s_fo not ha   ifue
 Tras_focus = 
        h\n"})']scoresal['total_op_rever (极端分: {tol']}**versal['symbop_re **{t部反转关注+= f"- 底t _tex  focusone:
       not Nsal isrever top_True
    ifcus = _fo hasn"
       .2f})\rength']:['st{top_long强度: ** (symbol']}_long['做多关注 **{top= f"- 强势us_text +foc       None:
  ng is notop_lose
    if tfocus = Fal
    has_：\n"= "**今日焦点**cus_text ne
    foNoelse _df.empty tynot extremif  ioc[0]ity_df.ilextremal = op_revers None
    tpty else5].emgth'] > 1.tren['sm_df[momentutum_dft momenc[0] if no].iloh'] > 1.5_df['strengtentum_df[mommomentumong =    top_l---
 部分  --- 1. 焦点信号  #[]
    
  elements = 
    
    "hr"} {"tag": rn hr(): retu}}
    defk_md"lar": ""tagontent, content": c{""text": iv", "d"tag": eturn {t): riv(contenxt_df te    deturn

ok_url: rebhoRE' in weOOK_URL_HER_WEBHYOU 'rl or_uebhook  if not wl')
  ook_urwebhishu_G.get('feONFI= Cbhook_url    we"
 ""消息。信息的飞书卡片关键发送一个统一的、包含所有  """构建并ity_df):
  tremrix, ex_matontiorreladf, ctum_momente, arket_stacation(mishu_notifiied_feef send_unif

async de)nt(tablconsole.pri           )
     3f}"corr:.]", f"{.2f}[/boldth']:'streng']-s[['strength"[bold]{lff})", ength']:.2tr{s['s}[/]({s['symbol']"[red]f})", f:.2gth']l['stren[/]({l']}['symbon]{low(f"[greeble.add_r   ta              :
   n pairs[:5]r icorfor l, s,               ght")
  stify="ri性", juumn("相关dd_colle.atab                )
ht"tify="rig"强度差", jusadd_column(  table.          
    fy="left")", justin("空头候选colum table.add_       
        "left")stify=, jumn("多头候选"e.add_colu    tabl           w")
 "bold yello_style=t", title"defaul style=交易策略池",e="⚖️ 配对titlble = Table(       ta     True)
    everse=], rngth''stre'] - x[1][trength: x[0]['smbda x(key=lartpairs.so      
          f pairs:          i   corr))
ow, s_row,ppend((l_r pairs.a                        
   配对4: # 寻找低相关性的0. corr < if                        s_sym]
 .loc[l_sym,ion_matrixrelat corr =cor                    
    mns:_matrix.coluationrrels_sym in cond trix aion_main correlat l_sym       if        ']
      ['symbol, s_rowsymbol']l_row[', s_sym =       l_sym     
         iterrows():hort_cands.ow in s  for _, s_r            rows():
  _cands.iterrow in long_, l_     for       rs = []
          pail(5)
   f.taimomentum_ds = hort_cand        s5)
    ad(um_df.heentds = moman     long_c
       None: not on_matrix isrelati if cor    交易池 ---
    配对 # --- 3.
       nt(table)
priconsole.           ", risk)
 .1f}dx']:, f"{row['a}"ength']:.2fstr['], f"{rowsymbol'row['", d red]做空[/]_row("[bol   table.add          
   "05 else "] < -0.e'_ratow['funding r" if🧊费率<-0.05%"     risk =            terrows():
s.iort_, row in shor            f
 n()iole.add_sect tab.empty:rtsd not shoanmpty  longs.eot      if n    isk)
  f}", r']:.1f"{row['adx", th']:.2f}ng'stre, f"{row[ow['symbol']n]做多[/]", reegrrow("[bold add_able. t          ""
     e > 0.05 elsng_rate'] ['fundi if row5%"率>0.0 risk = "🔥费             s():
  rrowin longs.iteor _, row    f)
         "left", justify=n("风险提示"e.add_columtabl         
   t")ify="righjustADX", olumn("add_c table.    
       y="right") justif("强度分",lumn.add_co       table")
     an"cy style=对",_column("交易   table.add   ")
      "centery=, justifn("方向"add_columable.    t")
        ld green_style="botitle", ault"def)", style=m Enginementu策略池 (Mo🚀 趋势ble(title=" Ta  table =          ts.empty:
not shorempty or  longs.f not    i)
    s('strength'ue5).sort_val0].tail( < -1.strength']ntum_df['[momeomentum_dfts = m shor)
       (5.head'] > 1.0]df['strength[momentum_mentum_dfongs = mo    l   
 pty:mentum_df.emot mof n
    i/趋势池 ---. 动量-- 2
    # -le)
    int(tabonsole.pr       c
 ow_data)e.add_row(*r    tabl  ']]
      ons'reas row[",al_score']}['tot+ [f"{rows_keys]  k in detailfor"N/A") , et(kls'].gaiet+ [row['d'symbol']] data = [row[  row_    s():
      _df.iterrowin extremityw ro,   for _=50)
      widthax_"left", mstify=", ju"得分原因column(add_table.    )
     yellow""boldtyle=", sify="centerst", julumn("总分cotable.add_        
")fy="right)[0], justit('_'key.splicolumn(  table.add_  
        eys:_klsetai in dkey        for se []
_df.empty el extremity)) if nots'].keys(aildetdf.iloc[0]['ity_list(extrem = _keysls      detaian")
  style="cy", "交易对umn(ble.add_col    ta   )
 ta"d magen"bolitle_style=", tefault, style="dEngine)" (Extremity 转策略池"🚨 反e=(titl= Table      table mpty:
  _df.extremity not e    if ---
转池. 极端值/反# --- 1    

)ue") style="bl/bold]",tails']})[te['det_sta']} ({marke'statusrket_state[| 当前状态: {mad]📊 市场扫描结果 ule(f"[bolrint(Rnsole.p"
    co"打印到终端。"观地函数，将所有分析结果美主显示  """:
  ty_df)rix, extremion_matrrelatif, co_dte, momentumet_stals(markoolay_all_pdisp=
def ============================================================================ ===
# =端显示与飞书通知)(终=== 5. 报告模块 ====
# =========================================================================s

# =symbol, top_atrixation_mf, correleturn d  
    r    ow]")
  }[/yell性矩阵计算失败: {e相关llow](f"[yeog.lonsole  c  as e:
    ion ept Exceptr()
    excf.cors_dix = returnmatron_ correlati            1:
    1] >e[haprns_df.sf retu    i    l')
    a(how='aldropnt_change().=1).pcis axseries,at(close_ncs_df = pd.cornetu   r         ) > 1:
eries_s(closeen     if l        
  ]
 ose'tamp')['clmesx('tiindet_temp_df.seies[sym] = ose_ser cl            s')
   ='m, unitmestamp']temp_df['time(ateti_dmp'] = pd.to['timesta  temp_df             
 ','v'])','l','closetamp','o','himesns=['t, columFrame(resatad.Dmp_df = p        te      ) > 1:
  len(resand ist) e(res, lsinstanc    if i      sults):
  relcv_ls, ohmboorr_sy(cin zipor sym, res     fs = {}
    rielose_se c   
       True)
     ptions=ceturn_exrr_tasks, reo.gather(*coit asynci awacv_results =  ohlols]
      in corr_symb) for s k']kbacooation_lrrel'cot=cfg[imie'], l_timeframontirelas, cfg['cortch_ohlcv(e.fe [exchangks =   corr_tas   
     
     T')'BTC/USDinsert(0, mbols.: corr_sycorr_symbols in USDT' not   if 'BTC/     数量
]  # 限制tolist()[:20mbol'].= df['symbols sy       corr_
    try:
 rix = Nonematrelation_    cor性矩阵
# 6. 计算相关 
    h'])
   ngtset=['streropna(subx().deset_indee).rcending=Falsh', asrengtlues('st df.sort_va
    df =df.columns) in  factor_zif() ts'].itemsor_weigh'factin cfg[r_z, weight z] for factoctor_t * df[fa(weighgth'] = sumdf['stren终强度分
      # 5. 计算最.std()

  ctor] / df[faan())ctor].mef[fa[factor] - d] = (dftor}_z''{fac    df[f
    的因子可以公平比较，使得不同量纲core标准化 # Z-S     
   p_high)low,tor], p_p(df[facnp.clif[factor] =         dile(0.975)
tor].quantf[fac, d25)0.0].quantile( df[factorp_high =ow,    p_l    常值
 理，去除极端异 # 缩尾处']:
       'volume_24hgth', 'rel_strenn [for factor i
    )n + Z-Scorezatiori处理 (Winso4. 数据标准化  
    # ymbol')
  dex('slts).set_insuFrame(reDatapd.  df = 
  bols_symone, topame(), NtaFr.Dan pd: returesultsnot r
    if =1)
ceask, advann_t(scass.update   progre
         sult)ts.append(resullt: resure      if   re
    = await futuesult    r
         ):(tasksletedcompyncio.as_n as iureor fut
        f))=len(tasks", total场动量...yan]正在扫描市"[cask(d_tress.ad = progn_taskca      sogress:
  ")) as pr:>3.0f}%k.percentageasntage]{togress.perce("[pr, TextColumn BarColumn()"),n}riptioask.desc{tiption].descr"[progressumn(xtColTe(), olumnss(SpinnerC with Progres = []
   lt   resuymbols]
 n top_sbol icfg) for symache, _data_cmbol, btc, sys(exchangeorctum_faulate_moment = [calc
    tasks所有币种的动量因子 并发计算  # 3.")

  /yellow]: {e}[算将受影响对强度计基准数据失败，相yellow]获取BTC.log(f"[     consoles e:
   xception acept E'])
    exse','v,'l','clop', 'o','h'estam'tim, columns=[c_ohlcv[i]Frame(all_btata[tf] = pd.Dachec_data_c          btames']):
  g['timefrate(cf in enumer   for i, tf    _tasks)
 *btcather(.gwait asyncioohlcv = abtc_ll_    a   mes']]
 efrag['timcf) for tf in ck'] + 100ookbalimit=cfg['lUSDT', tf, ('BTC/_ohlcv.fetch[exchangec_tasks =        bttry:
     cache = {}
 btc_data_基准
   数据作为前获取BTC    # 2. 提olume']]

'top_n_by_v)][:CONFIG[T''/USDith(s.endswbols if ymr s in all_ss fo= [symbols op_      ted]")
  用默认列表。[/re}，将使交易对失败: {red]筛选le.log(f"[      conso
  e:tion as Excep except )
   。"量扫描个交易对进行动 green] /bold)}[symbols{len(top_en]greld 的 [bo最高已筛选出交易额g(f" console.lo    
   l'].tolist()'])['symbo_by_volumeNFIG['top_nad(COg=False).he', ascendin'quoteVolumet_values(olume_df.sorymbols = vp_sto        ])
   e')
     'quoteVolumet( t.gndsymbols aif s in all_ems() ers.it in all_tick s, tor      f     ]} 
 eVolume': t['quote'olum s, 'quoteV  {'symbol':          aFrame([
f = pd.Dat_d    volume()
    ckers_tietchxchange.fwait e= al_tickers        alry:
  N
    t筛选出topr，按交易额有ticke   # 1. 获取所
 
one, []rame(), NtaFpd.Das: return all_symbol  if not nge)
  ymbols(exchaetual_st fetch_perpbols = awaisym
    all_ngine']m_etuIG['momen cfg = CONF   阵。"""
和相关性矩描，并返回排序后的结果的动量扫"""执行全市场e):
    angcan(exchomentum_s_mrun
async def al_result
rn fin   retul
 mbo] = sy['symbol'inal_result   
    f        lues)])
 ghts[:len(vaghts=weialues, wei.average(v= npkey] result[   final_ue
     tin con           ]
[0 = valuest[key]_resul   final        的，不需要加权
  24h成交额是全局 #h':lume_24'vokey == if        _results]
 imeframel_tn alor res i, 0) f.get(keyalues = [res v):
       0].keys(ts[ame_resultimefrkey in all_']
    for ightsame_weefrim cfg['t   weights =t = {}
 ulal_res均
    fin权平多时间周期的结果进行加  # 对  e

urn Non retesults:timeframe_r if not all_
   tinue
    on          c:
   Exceptionept
        exc   })      change,
   ge': oi_han_rate, 'oi_c funding':ateunding_r   'f           
  si,w, 'rsi': rbbw': bb, 'dx': adx      'a          lume_24h,
e_24h': vogth, 'volumrel_strenth': 'rel_streng           
     {lts.append(rame_resuall_timef    

        : passtionepxc  except E         0.0)
 lume', eVo'quoter.get(_24h = tick  volume             symbol)
 ticker(e.fetch_ait exchangicker = aw   t            try:
          0
   e_24h = 0.lum         vo指标
   流动性T计价），作为时成交额（以USD   # 获取24小                
  pass
    t Exception:excep            
 转换为百分比100 #0.0) * , undingRate'get('fing_data.= fundding_rate      fun          bol)
 g_rate(symundinch_fhange.fetait exc = awfunding_data              try:
    
          0.0rate = ding_  fun          

ption: passxcet E   excep     * 100
    oc[0]) - 1) ue'].ilInterestVal_df['openloat(oi) / fe'].iloc[-1]lustVa'openIntereoat(oi_df[hange = (fl        oi_c         
   算OI变化  # 使用名义价值计        
          y)i_histor.DataFrame(o pdoi_df =                  :
   > 1(oi_history)tory and len_his   if oi           ck'])
  kbat=cfg['loo limiimeframe, tol,story(symbest_hinteretch_open_iange.fit exch = awaory oi_hist    
            try:     
      0.0i_change =      o标
       风险/情绪指 # 获取衍生品数据作为    
                um
    btc_momentmomentum - = rengthst   rel_         
    ) * 100']-2] - 1okbackc[-cfg['lolose'].ilo['c / btc_dfloc[-2]lose'].i (btc_df['cum =  btc_moment             meframe]
 [tidata_cache = btc_   btc_df          
   ot None:e) is namget(timefre.ta_cachda btc_      if   
   tummen = motrength   rel_s量
         身动量 - BTC动# 相对强度 = 自     
                  1) * 100
 ] - -2'lookback']loc[-cfg[.ilose']'cf[/ dloc[-2] close'].i (df['omentum =     m  OC)
     价格变化率 (R量因子：心动   # 核            
        oc[-2]
 '].ildf['RSI_14rsi =         2]
    iloc[-.0'].ack"]}_2okbloW_{cfg["f'BBw = df[bb        [-2]
    ilocck"]}'].okba"locfg[f[f'ADX_{dx = d  a         
 指标值，避免未来函数K线（倒数第二根）获取# 从已收盘的          

   2)ack'],, cfg['lookbf['close']nds(dger_balinte_bol'] = calculak"]}_2.0okbaclog["[f'BBW_{cf df bb_percent,b, lower_bb, upper_b           kback'])
cfg['loo'], ], df['closef['low', df['high']dx(de_alat'] = calcu"]}okbackcfg["lo_{f'ADXf[         d    14)
close'],df['rsi(= calculate_4'] _1RSI      df['标
        # 计算基础指          
 # 数据不足则跳过
ontinue c27:ookback'] + cfg['l < (df)   if len    '])
     e', 'volume', 'clos'low 'high', n', 'opeestamp',['timolumns=ohlcv, c.DataFrame(pdf =   d     
      + 100)kback']it=cfg['loorame, lim timef(symbol,etch_ohlcvhange.f await exc    ohlcv =确
        计算准据，多获取一些以保证指标# 获取K线数     :
           try  rames']:
  'timefg[n cframe ior timef  f= []
  ults e_restimefram
    all_"相关因子。""币种计算多时间周期的动量"为单个   ""):
 cfgache, c_data_cymbol, bt, ss(exchangeoracttum_flate_momenc def calcu---
asyn4.3 动量引擎 --- 
# g=False)
ascendinl_score', 'totat_values(ults).sorrame(respd.DataFeturn ame()
    rFratareturn pd.Dsults: not re  if 
  nue
ti        con   
  Exception:eptexc        
ons)})easjoin(r: ", ".ons''reasdetails, 'details': e, al_scor tototal_score':, 't symbolsymbol':.append({' results         e']:
      scorreshold_'alert_th= cfg[core >total_s     if 该机会
       总分超过阈值，则记录如果       #     
 ish"
ll] = "RSI Buvergence'['Dietails      d
          )")bonus']}ivergence_'dI看涨背离({cfg[append(f"RS  reasons.            s']
  e_bonu['divergence += cfg_scoral   tot           
  _14']):RSI'], df['df['closeivergence(ullish_dfind_bd lumns an4' in df.coRSI_1if '          
  ，如果存在则加分# 检查是否存在看涨背离            
     
       # 只取最高分break                           )
  core})"{s<{p*100}%([0]}r.split('_')"{factos.append(f     reason                    
    score_score +=al    tot                    :
    p]actor][lds[f< threshot_value  if curren                     items():
  ow']._map['le in score p, scor         for    :
       ze']ity_squeevolatil 'tion_low',low', 'devialator_in ['oscilore_type     if sc        点
     判断是应该找低点还是高      #         e_map']
 = cfg['scorcore_map         s      ype']
   = f_cfg['ttypecore_         s  
     tinue
olds: coneshthrnot in f factor   i           
   f}"e:.2rent_valu] = f"{curtordetails[fac           inue
     alue): content_vcurrna(  if pd.is            
  nanns else np.df.columctor in  if fa[-1]or].ilocue = df[factvalent_        curr       items():
 rs'].actofg in cfg['ff_cor factor,      f      进行比较，并计分
 历史分位数好的 将当前值与校准  #   
       [symbol]
ion_cache calibratresholds =   th        [], {}
 tails = 0, asons, de rel_score,    tota   00

      1) * 1a200 -lose'] / emdf['cct'] = (ema200_pf['price_vs_          d
   200)se'],lote_ema(df['cula calc =00      ema2, 2)
      e'], 20'closs(df[nger_bandbollilculate_2.0'] = ca_20_'BBW, df[0']0_2.BP_2r_bb, df['Br_bb, loweppe          u  ], 14)
e'['volume'], df], df['closf['low'], digh'df['hfi(ate_mlcul_14'] = cadf['MFI          4)
   1df['close'],si(e_rulatalc_14'] = c df['RSI      
     的指标值   # 计算当前最新
         
ontinueache: cibration_cin cal symbol not 200 or< f)     if len(d      =True)

  placeestamp', inndex('timset_i       df.   ')
   unit='msstamp'],imee(df['tto_datetim] = pd.p'mestam['ti        df
    ume'])se', 'vol', 'cloow', 'l'highpen', stamp', 'ons=['timehlcv, columrame(of = pd.DataF          d250)
  t=mi, limeframe'], cfg['tihlcv(symbolnge.fetch_ochav = await ex      ohlc数据
       获取用于分析的近期 #          y:
 
        tr']:mbolsg['scan_symbol in cf  for sy []
  results =   )

 .DataFrame(rn pdache: retu_cration not calibor['enabled'] t cfg
    if noty_engine']tremiG['exNFICO =    cfg。"""
 极值的反转机会寻找指标达到历史""扫描指定币种，   "change):
 (ex_scanemityn_extrnc def ru"))

asyd green]/bol]校准程序完成[bold green("[ulele.print(Ronso)
    c值校准失败: {e}"n] 的动态阈ol}[/cya [cyan]{symb ❌e.log(f"       consol   n as e:
   eptioxcept Exc e  。")
     n] 的动态阈值校准成功}[/cyaolyan]{symb"  ✅ [cnsole.log(f       cods
     sholymbol_thre] = sbolon_cache[symalibrati           cct()
 _di']).toilesercentile(cfg['puantname].qtor_acname] = df[for_actlds[fbol_threshosym                  所有百分位数值
  该因子的     # 计算并存储              
 :lumnsf.coe in dctor_namf fa        i     
   '].keys():actorsme in cfg['for factor_na     f
       lds = {}eshothrbol_         sym0

    10) * 1 / ema200 -['close']] = (df200_pct'ce_vs_ema    df['pri       200)
 ose'], e_ema(df['clalculat c    ema200 =   2)
      e'], 20,(df['closands_bollinger_blate'] = calcu['BBW_20_2.0'], df2.0['BBP_20_dflower_bb, ,  upper_bb          )
 e'], 14um, df['voldf['close']df['low'], gh'], df['hii(late_mfcu = calI_14']   df['MF      ], 14)
   close'['dflculate_rsi( ca['RSI_14'] =         df因子
    # 计算所有需要校准的          
 True)
', inplace=mpx('timestaf.set_inde     d      
 )unit='ms'p'], mestamme(df['tipd.to_datetiamp'] = est    df['tim
        olume'])'close', 'v'low', igh', 'open', 'himestamp',  columns=['tohlcv,d.DataFrame(      df = p])
      k_days'lookbaccalibration_['=cfgitrame'], limmeffg['ti, colymbetch_ohlcv(schange.fait exaw =        ohlcv   ry:
       tbols']:
   yman_sg['scsymbol in cf 
    for )
   ]")bold blue擎校准程序[/e]启动极端值引 blu("[boldt(Rulee.prin  consol: return
  d'] cfg['enablef not']
    ity_enginetremi['exg = CONFIGache
    cfion_cal calibrat"
    glob阈值。""位数，作为动态年的历史分计算各指标在过去一一次，""在程序启动时运行 "):
   angegine(exchemity_ene_extrrat calib
async def擎 --- 4.2 极端值引# ---

}n(reasons)", ".joi': , 'detailstatus_scoreore': sstatus, 'sc{'status':     return 

        中性"= "⚪ status     else:  "🔴 看跌"
 =40: statusscore <= -atus_  elif st
   "🧊 极度看跌"tus =sta:  -80re <= status_sco elif🟢 看涨"
   tus = " stae >= 40:tus_scorstaelif     "
 "🔥 极度看涨 status =0:re >= 8co if status_s
   最终市场状态. 根据总分判断

    # 3  pass     ception:
 Ex  except 
   20 # 市场弱势 -=_score status0:dth_pct < 4  elif brea           # 市场强势
 e += 20tatus_scor> 60: st breadth_pc      if ")
      th_pct:.0f}%:{breadpend(f"广度  reasons.ap
           * 100ls)symbo/ valid_ount _ema_coveab = (_pctdth     brea       mbols > 0:
id_sy  if val
          
     1t +=_ema_counabove            :
        val-1] > ema_loc[e'].ios'cl df[  if              -1]
oc[.ilth_ema'])eadfg['bre'], cclosate_ema(df[' = calcul    ema_val   
         v']),'close','h','l'tamp','o','es['timcolumns=rame(res, f = pd.DataF        d       = 1
 ymbols +alid_s  v            1:
  dth_ema'] + = cfg['breas) >len(re, list) and ce(restan  if isins          lts:
 in resu   for res0
      = mbols   valid_syt = 0
     ema_coun   above_     
     
   ons=True)cepti, return_exksher(*tasio.gatsync = await a    results避免超时
    数量 # 限制:20]] p_symbols[for s in to)  2ema'] +'breadth_fg[imit=cmeframe'], lh_tifg['breadtlcv(s, ce.fetch_oh= [exchang  tasks 
         try:
 上）期均线之有多少主流币处于短度（# 2. 计算市场广  ntinue

  co           eption:
 pt Exc   exce}")
     a_period_EMA{em[0]}>Dt('/')symbol.splippend(f"{   reasons.a                dd
  score_a_score +=atus         st         权重更高
  A200e 25 # 站上EMd == 50 elsif ema_perio5  1d =ore_ad        sc          a_val:
  ose > em if cl             oc[-1]
  _period).il, emase']'clo_ema(df[te = calcula    ema_val            eriods']:
_pily_ema in cfg['daeriod for ema_p       oc[-1]
    se'].il['cloclose = df               
         ue
inont): ceriods']aily_ema_px(cfg['df) < maen(d       if l])
      'volume', 'close',, 'low'h''higp', 'open', timestams=[' column(ohlcv,ramef = pd.DataF   d        ) + 5)
 ds']erioma_p['daily_efg(caxt=mlimibol, '1d', h_ohlcv(symange.fetc await exchohlcv =             try:
       
s']:'base_symboll in cfg[  for symbo趋势
  H）的日线心资产（BTC/ET1. 分析核    # ns = []

 reaso
    = 0atus_scores']
    st_statuG['marketg = CONFI"
    cf荡市""市还是震前是牛市、熊观市场状态，判断当""计算宏    "s):
 top_symbol(exchange,ket_statusf get_marync de---
as市场状态引擎  --- 4.1 
#=
==============================================================================
# 心分析引擎 ==
# === 4. 核=========================================================================

# =====sealurn Fe
    retls return Fa
       on:pticet Exexcepue
      return Tr         min:
      earlier__price_min >tor_at if indica     n()
      a.milier_datr_min = earearlie            > 5:
a) rlier_datea     if len(
   dx]ice_min_iprator[: = indiclier_dataear     
   低点低点之前是否有更高的指标在价格最       # 检查
 
        ice_min_idx][prdicator.locin= e_min t_pric indicator_a()
       dxminx = price.iprice_min_id    低点
    # 找到最近的        
           lse
 n Faturre       10:
     ) < dicatorinor len() < 10 (pricef len   i    背离检测逻辑
     # 简化的back)
    l(lookeries.tai_s indicatorback),.tail(lookprice_seriesator = ce, indic  pri:
          try""
指标未创新低）"涨背离（价格创更低低点，   """检测看30):
 okback=es, lo_seris, indicatoriece(price_serh_divergenlisd_bulin
def f]
/USDT''XRPOGE/USDT', 'DSDT', SDT', 'BNB/UT', 'SOL/UT', 'ETH/USDSD'BTC/U    return [  
  常见的交易对作为备选  # 返回一些)
      [/red]"表失败: {e}ed]获取永续合约列f"[re.log(sol     conas e:
   xception  except E  s
 oletual_symburn perp    ret   ]
 True)active', ('t.get and marke) == 'swap't.get('type'   if marke                       ) 
 ets.items(ket in mark marmbol,l for syymbo[sols = al_symbpetu
        perets()oad_markexchange.lwait arkets = a
        m"
    try:交易对"""获取所有永续合约:
    ""hange)bols(excrpetual_sym_penc def fetch==
asy======================================================================= ========
#工具函数  辅助# === 3.=
===================================================================# ==========

 Rich终端输出实例ole()     # Consnsole =据的全局缓存
co储校准数用于存= {}  # ion_cache -
calibrat- 全局变量 --
}

# --  }  低总分
触发警报的最#          : 50,    d_score'rt_threshol 'ale  加分
     的额外涨背离时现看     # 发             bonus': 30,ergence_     'div},
   
        y分位数得分高于0.90: 10} # 5: 20, .940, 0 {0.99: high': '         位数得分
  ,  # 低于x分10}.10:  0.05: 20, 00,01: 4low': {0.       '数
     的分 # 分位数对应                       ': {    'score_map},
            
   预示着潜在的大波动史低位）分越高，度，越窄（历带宽,  # 布林ty_squeeze'}volatili: 'ype'2.0': {'t20_       'BBW_越大分越高
     偏离，负离度价格与EMA200的偏'}, # on_lowatievi'type': 'd': {_ema200_pct 'price_vs           )
高 (超卖越低分越 # 布林带百分比B，    w'},  cillator_lo'type': 'os {P_20_2.0':   'BB         分越高
MFI，资金流指标，越低  #    },     r_low'atope': 'oscill14': {'ty    'MFI_       
 越高SI，越低分 # R'},         _lowcillatoros'type': '_14': {  'RSI       
   寻找反转机会的因子  # 用于                      rs': {          'facto   要计算的历史分位数
], # .995, 0 0.90, 0.905, 0.10, 0.: [0.01,rcentiles'      'pe期
  K线周   # 极端值分析的                    me': '1d',timefra  '   算历史分位数
   天数，用于计  # 校准时回看的    5,  ': 36ysokback_daibration_lo 'cal  
     定扫描高市值币种/USDT'], # 指INK 'LLDO/USDT',P/USDT', 'DT', 'XRGE/US'DOUSDT', USDT', 'BNB/, 'SOL/'ETH/USDT'T', ': ['BTC/USDols  'scan_symb     # 是否启用此引擎
                       e,   bled': Tru  'ena
      e': {enginxtremity_--
    'e置 - 极端值引擎配
    # --- },
  算相关性的回看天数
     # 计0,          : 9okback'ation_lo    'correl
    计算相关性的K线周期        # ',   1deframe': 'n_tim 'correlatio,
               }(成交额) 的权重
动性 # 流                 h_z': 0.4, 'volume_24          权重
 lpha) 的相对强度 (A       # ,        ngth_z': 0.6strel_      're
      ，资金费率已移出子权重4版因   # V               : {    r_weights' 'facto  期
     和指标的回看周 动量        #                  20,: lookback'     '    各时间周期的权重
,         # 0.4] [0.6,e_weights':ram  'timef
      时间周期分析# 多            '],  ', '1des': ['4h  'timefram    ne': {
  ngi 'momentum_e ---
   - 动量引擎配置

    # --
    },用的EMA周期算市场广度所       # 计              ma': 20,  eadth_e     'br  场广度的K线周期
 # 计算市             '4h',   rame':readth_timef    'b   键EMA周期
 断日线趋势的关      # 判    0, 200],iods': [5ily_ema_per      'da盘方向的核心资产
  于判断大T'], # 用TH/USD 'E['BTC/USDT',mbols':  'base_sy {
       t_status':arke   'm ---
 擎配置- 市场状态引 --  #

  URL !!!Webhook 器人 请替换为你自己的飞书机', # !!!OK_URL_HEREBHOYOUR_WE2/hook//v-apis/botpen/oishu.cnopen.fetps:// 'htrl':_uhu_webhook描
    'feis种进行动量扫50个币额筛选前交易# 按24小时,          ume': 50ol_by_v    'top_n秒 = 1小时
），3600扫描间隔时间（秒  #  3600,l_seconds':ervantn_ica
    's局设置 ------ 全  # IG = {
  NF===
CO=========================================================================
# ====心 === 2. 统一配置中
# ============================================================================ ===urn mfi

#w))
    retloative_flow / negsitive_f (1 + po0 - (100 /i = 10   
    mfsum()
 dow=period).olling(wint(), 0).r_price.shifal typicice <(typical_prow.whereoney_flow = me_fl negativum()
   period).sw=ng(windo, 0).rolliice.shift()> typical_prprice e(typical__flow.wher = moneyive_flow posit   
    
umevol * al_price = typic  money_flow / 3
  lose) + c (high + lowcal_price =
    typi"""算MFI指标   """计d=14):
 , perioolumew, close, vh, lolate_mfi(hig
def calcu
 adxurnn()
    ret=period).mea(window dx.rolling  adx =inus_di)
  (plus_di + mus_di) / _di - min * abs(plus   dx = 100
    
 ).mean())w=periodng(windor.rolli/ tiod).mean() (window=perllingus_dm.ro* (mindi = 100 nus_    min())
meaperiod).ndow=g(wiollin.rean() / trriod).mndow=peg(wim.rollins_d= 100 * (pludi s_  plu 
      < 0] = 0
m[minus_dm minus_d] = 0
    0us_dm < plus_dm[pldiff()
   ow._dm = -lminus
    .diff() = highplus_dm     
 (axis=1)
  xis=1).max, a tr2, tr3]r1,t([t= pd.conca))
    tr ift(se.sh(low - clo = abs
    tr3shift())ose.h - cl abs(hig
    tr2 = high - low  tr1 =标"""
  "计算ADX指"    ":
14)riod= close, peigh, low,late_adx(h
def calcuwidth
b_, b, bb_percentr_band, lowen upper_banda
    returand) / smr_blower_band - uppe= (b_width 
    bnd)- lower_baand r_b (uppeand) / lower_bes -ent = (pricerc)
    bb_pd * std_dev = sma - (st  lower_bandd_dev)
  (std * st+ band = sma pper_td()
    u.sw=period)indo.rolling(wespric
    std = d).mean()window=periog(ices.rollin  sma = pr
  布林带""""""计算
    =2):d_devd=20, stes, perio(pricbandsbollinger_f calculate_
de)
.mean(pan=period)s.ewm(surn priceret"""
    A指标计算EM """eriod):
   , p_ema(pricesef calculate

di return rs + rs))
    (100 / (1 100 -  rsi =
   loss rs = gain /
   iod).mean()indow=perling(w< 0, 0)).rollta re(de-delta.whe = (  loss  )
riod).mean((window=pelling> 0, 0)).rodelta elta.where(ain = (d)
    gf(ices.dif delta = pr"
   "I指标"""计算RS"    4):
od=1ces, peririe_rsi(p calculatef====

d================================================================== ========