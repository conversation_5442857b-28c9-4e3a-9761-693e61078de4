#!/usr/bin/env python3
# 测试代理配置是否正常工作

import sys
import asyncio
import ccxt.pro
from rich.console import Console

# 修复在 Windows 上的 aiodns/asyncio 兼容性问题
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

console = Console()

# 常见的代理端口列表
COMMON_PROXY_PORTS = [7890, 1080, 8080, 10809, 1087, 7891]

# 代理配置 - 会自动检测可用端口
PROXY_CONFIG = {
    'enabled': True,  # 设置为 False 可以禁用代理
    'auto_detect': True,  # 自动检测可用的代理端口
    'manual_port': 7890,  # 手动指定端口（当auto_detect=False时使用）
}

def create_exchange():
    """创建配置好代理的交易所实例"""
    exchange_config = {
        'enableRateLimit': True,
        'options': {'defaultType': 'future'}
    }
    
    # 如果启用了代理，添加代理配置
    if PROXY_CONFIG['enabled']:
        exchange_config['proxies'] = {
            'http': PROXY_CONFIG['http'],
            'https': PROXY_CONFIG['https']
        }
        console.log(f"[yellow]已启用代理: {PROXY_CONFIG['http']}[/yellow]")
    else:
        console.log("[yellow]未启用代理，直连模式[/yellow]")
    
    return ccxt.pro.binance(exchange_config)

async def test_connection():
    """测试连接是否正常"""
    exchange = create_exchange()
    
    try:
        console.log("[cyan]正在测试连接...[/cyan]")
        
        # 测试获取市场信息
        console.log("1. 测试获取市场信息...")
        markets = await exchange.load_markets()
        console.log(f"   ✅ 成功获取 {len(markets)} 个市场")
        
        # 测试获取BTC价格
        console.log("2. 测试获取BTC/USDT价格...")
        ticker = await exchange.fetch_ticker('BTC/USDT')
        console.log(f"   ✅ BTC/USDT 当前价格: ${ticker['last']:,.2f}")
        
        # 测试获取K线数据
        console.log("3. 测试获取K线数据...")
        ohlcv = await exchange.fetch_ohlcv('BTC/USDT', '1h', limit=5)
        console.log(f"   ✅ 成功获取 {len(ohlcv)} 根K线数据")
        
        console.log("[bold green]🎉 所有测试通过！代理配置正常工作。[/bold green]")
        
    except Exception as e:
        console.log(f"[bold red]❌ 连接测试失败: {e}[/bold red]")
        console.log("[yellow]请检查：[/yellow]")
        console.log("1. 代理软件是否正在运行")
        console.log("2. 代理端口是否正确 (常见端口: 7890, 1080, 8080)")
        console.log("3. 代理软件是否允许本地连接")
        console.log("4. 如果不需要代理，请将 PROXY_CONFIG['enabled'] 设置为 False")
        
    finally:
        await exchange.close()

if __name__ == "__main__":
    try:
        asyncio.run(test_connection())
    except KeyboardInterrupt:
        console.log("\n[yellow]测试被用户中断[/yellow]")
    except Exception as e:
        console.log(f"[bold red]测试过程中发生错误: {e}[/bold red]")
