#!/usr/bin/env python3
"""
网络连接问题诊断和修复脚本
"""

import asyncio
import ccxt
import ccxt.pro
import requests
import time
from rich.console import Console
from rich.table import Table

console = Console()

def test_basic_connectivity():
    """测试基础网络连接"""
    console.print("[bold blue]测试基础网络连接...[/bold blue]")
    
    test_urls = [
        "https://www.google.com",
        "https://api.binance.com/api/v3/ping",
        "https://fapi.binance.com/fapi/v1/ping",
    ]
    
    results = []
    for url in test_urls:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                results.append((url, "✓ 成功", response.status_code))
            else:
                results.append((url, "✗ 失败", response.status_code))
        except Exception as e:
            results.append((url, f"✗ 错误: {str(e)[:50]}", "N/A"))
    
    table = Table(title="网络连接测试")
    table.add_column("URL", style="cyan")
    table.add_column("状态", style="white")
    table.add_column("状态码", style="yellow")
    
    for url, status, code in results:
        table.add_row(url, status, str(code))
    
    console.print(table)
    return all("成功" in result[1] for result in results)

def test_ccxt_sync():
    """测试同步CCXT连接"""
    console.print("\n[bold blue]测试同步CCXT连接...[/bold blue]")
    
    try:
        # 使用同步版本测试
        exchange = ccxt.binance({
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {'defaultType': 'spot'}  # 先测试现货
        })
        
        # 测试获取服务器时间
        server_time = exchange.fetch_time()
        console.print(f"✓ 服务器时间获取成功: {time.ctime(server_time/1000)}")
        
        # 测试获取市场信息
        markets = exchange.load_markets()
        console.print(f"✓ 市场信息获取成功: {len(markets)} 个交易对")
        
        return True
        
    except Exception as e:
        console.print(f"✗ 同步CCXT连接失败: {e}")
        return False

async def test_ccxt_async():
    """测试异步CCXT连接"""
    console.print("\n[bold blue]测试异步CCXT连接...[/bold blue]")
    
    try:
        # 使用异步版本测试
        exchange = ccxt.pro.binance({
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {'defaultType': 'spot'}  # 先测试现货
        })
        
        # 测试获取服务器时间
        server_time = await asyncio.wait_for(
            asyncio.to_thread(exchange.fetch_time), 
            timeout=30
        )
        console.print(f"✓ 异步服务器时间获取成功: {time.ctime(server_time/1000)}")
        
        # 测试获取市场信息
        markets = await asyncio.wait_for(
            exchange.load_markets(),
            timeout=60
        )
        console.print(f"✓ 异步市场信息获取成功: {len(markets)} 个交易对")
        
        await exchange.close()
        return True
        
    except asyncio.TimeoutError:
        console.print("✗ 异步连接超时")
        return False
    except Exception as e:
        console.print(f"✗ 异步CCXT连接失败: {e}")
        return False

async def test_futures_connection():
    """测试期货连接"""
    console.print("\n[bold blue]测试期货API连接...[/bold blue]")
    
    try:
        # 测试期货API
        exchange = ccxt.pro.binance({
            'enableRateLimit': True,
            'timeout': 30000,
            'options': {'defaultType': 'future'}  # 期货
        })
        
        # 测试获取期货市场信息
        markets = await asyncio.wait_for(
            exchange.load_markets(),
            timeout=60
        )
        
        # 筛选永续合约
        perpetuals = [
            s for s, m in markets.items()
            if m.get('swap') and m.get('active') and 
               m.get('settle') == 'USDT' and m.get('quote') == 'USDT'
        ]
        
        console.print(f"✓ 期货市场连接成功: {len(perpetuals)} 个永续合约")
        
        await exchange.close()
        return True
        
    except Exception as e:
        console.print(f"✗ 期货连接失败: {e}")
        return False

def create_fixed_scanner_config():
    """创建修复后的扫描器配置"""
    console.print("\n[bold blue]创建修复后的配置...[/bold blue]")
    
    fixed_config = """
# 修复后的扫描器配置
import ccxt.pro
import asyncio

def create_robust_exchange():
    \"\"\"创建更稳定的交易所连接\"\"\"
    return ccxt.pro.binance({
        'enableRateLimit': True,
        'timeout': 60000,  # 增加到60秒
        'options': {
            'defaultType': 'future',
            'recvWindow': 10000,  # 增加接收窗口
        },
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    })

async def robust_load_markets(exchange, max_retries=5):
    \"\"\"更稳定的市场数据加载\"\"\"
    for attempt in range(max_retries):
        try:
            markets = await asyncio.wait_for(
                exchange.load_markets(), 
                timeout=120  # 2分钟超时
            )
            return markets
        except asyncio.TimeoutError:
            print(f"尝试 {attempt + 1}: 超时，重试中...")
            await asyncio.sleep(5)
        except Exception as e:
            print(f"尝试 {attempt + 1}: {e}")
            await asyncio.sleep(10)
    
    raise Exception("无法加载市场数据")
"""
    
    with open("connection_fix.py", "w", encoding="utf-8") as f:
        f.write(fixed_config)
    
    console.print("✓ 修复配置已保存到 connection_fix.py")

def suggest_solutions():
    """提供解决方案建议"""
    console.print("\n[bold yellow]解决方案建议:[/bold yellow]")
    
    solutions = [
        "1. 检查网络连接是否稳定",
        "2. 尝试使用VPN或代理",
        "3. 增加超时时间和重试次数",
        "4. 使用不同的DNS服务器 (8.8.8.8, 1.1.1.1)",
        "5. 检查防火墙设置",
        "6. 尝试在不同时间段运行",
        "7. 考虑使用其他交易所API"
    ]
    
    for solution in solutions:
        console.print(f"  {solution}")

async def main():
    """主测试函数"""
    console.print("[bold]网络连接诊断工具[/bold]\n")
    
    # 基础连接测试
    basic_ok = test_basic_connectivity()
    
    # 同步CCXT测试
    sync_ok = test_ccxt_sync()
    
    # 异步CCXT测试
    async_ok = await test_ccxt_async()
    
    # 期货连接测试
    futures_ok = await test_futures_connection()
    
    # 总结
    console.print("\n" + "="*60)
    console.print("[bold]诊断结果:[/bold]")
    console.print(f"基础网络: {'✓ 正常' if basic_ok else '✗ 异常'}")
    console.print(f"同步CCXT: {'✓ 正常' if sync_ok else '✗ 异常'}")
    console.print(f"异步CCXT: {'✓ 正常' if async_ok else '✗ 异常'}")
    console.print(f"期货API:  {'✓ 正常' if futures_ok else '✗ 异常'}")
    
    if all([basic_ok, sync_ok, async_ok, futures_ok]):
        console.print("\n[bold green]✓ 所有连接正常！扫描器应该可以正常工作[/bold green]")
    else:
        console.print("\n[bold red]✗ 检测到连接问题[/bold red]")
        create_fixed_scanner_config()
        suggest_solutions()
        
        # 提供临时解决方案
        console.print("\n[bold cyan]临时解决方案:[/bold cyan]")
        console.print("1. 修改 scanner_optimized.py 中的超时设置:")
        console.print("   timeout: 30000 → 60000")
        console.print("2. 增加重试次数:")
        console.print("   max_retries: 3 → 5")
        console.print("3. 使用更稳定的配置:")
        console.print("   python run_scanner.py --config conservative --top-n 10")

if __name__ == "__main__":
    asyncio.run(main())